'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import { motion } from 'framer-motion';

interface OptimisticLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  prefetch?: boolean;
}

export default function OptimisticLink({ 
  href, 
  children, 
  className = '', 
  prefetch = true 
}: OptimisticLinkProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsNavigating(true);
    
    startTransition(() => {
      router.push(href);
    });
    
    // Reset after a short delay
    setTimeout(() => {
      setIsNavigating(false);
    }, 100);
  };

  return (
    <Link 
      href={href} 
      className={`${className} ${isNavigating ? 'opacity-80' : ''} transition-opacity duration-100`}
      onClick={handleClick}
      prefetch={prefetch}
    >
      <motion.div
        animate={isNavigating ? { scale: 0.98 } : { scale: 1 }}
        transition={{ duration: 0.1 }}
      >
        {children}
      </motion.div>
    </Link>
  );
}
