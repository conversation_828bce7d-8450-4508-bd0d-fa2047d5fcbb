"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/FeaturesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/landing/FeaturesSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/NoSymbolIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,ClockIcon,Cog6ToothIcon,CpuChipIcon,CurrencyDollarIcon,NoSymbolIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Intelligent Role Routing\",\n        description: \"AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.\",\n        color: \"text-blue-600\",\n        bgColor: \"bg-blue-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Unlimited API Requests\",\n        description: \"No request limits, no usage caps, no overage fees. Pay only for your own API costs while enjoying unlimited access to 300+ AI models.\",\n        color: \"text-green-600\",\n        bgColor: \"bg-green-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Enterprise Security\",\n        description: \"Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.\",\n        color: \"text-purple-600\",\n        bgColor: \"bg-purple-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Comprehensive Analytics\",\n        description: \"Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.\",\n        color: \"text-orange-600\",\n        bgColor: \"bg-orange-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Performance Optimization\",\n        description: \"First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.\",\n        color: \"text-indigo-600\",\n        bgColor: \"bg-indigo-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Cost Optimization\",\n        description: \"Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.\",\n        color: \"text-emerald-600\",\n        bgColor: \"bg-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"300+ AI Models\",\n        description: \"Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.\",\n        color: \"text-red-600\",\n        bgColor: \"bg-red-50\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_ClockIcon_Cog6ToothIcon_CpuChipIcon_CurrencyDollarIcon_NoSymbolIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        title: \"Advanced Routing Strategies\",\n        description: \"Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.\",\n        color: \"text-cyan-600\",\n        bgColor: \"bg-cyan-50\"\n    }\n];\nfunction FeaturesSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white py-20 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        gridSize: 45,\n                        opacity: 0.06,\n                        color: \"#000000\",\n                        variant: \"premium\",\n                        animated: true,\n                        className: \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: [\n                                            \"Enterprise-Grade\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                children: [\n                                                    ' ',\n                                                    \"AI Infrastructure\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                        children: \"RouKey provides military-grade security, intelligent routing, and comprehensive analytics for the most demanding AI workloads. Built for scale, designed for performance.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                                children: features.slice(0, 4).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white rounded-2xl p-8 shadow-lg border border-gray-200 hover:shadow-xl hover:border-[#ff6b35]/30 transition-all duration-300 group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"h-8 w-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors duration-300\",\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, feature.title, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full overflow-hidden leading-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"relative block w-full h-20\",\n                            \"data-name\": \"Layer 1\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 1200 120\",\n                            preserveAspectRatio: \"none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M1200 120L0 16.48 0 0 1200 0 1200 120z\",\n                                className: \"fill-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-[#ff6b35] via-[#f7931e] to-[#ff6b35] py-32 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                gridSize: 55,\n                                opacity: 0.12,\n                                color: \"#ffffff\",\n                                variant: \"tech\",\n                                animated: true,\n                                glowEffect: true,\n                                className: \"absolute inset-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                    children: features.slice(4).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            transition: {\n                                                delay: index * 0.1\n                                            },\n                                            className: \"bg-white rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl hover:border-white/40 transition-all duration-300 group backdrop-blur-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"h-8 w-8 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors duration-300\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 leading-relaxed\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, feature.title, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\FeaturesSection.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\n"));

/***/ })

});