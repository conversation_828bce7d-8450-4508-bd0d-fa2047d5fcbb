'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, PlayIcon, SparklesIcon, BoltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import OptimisticLink from '@/components/ui/OptimisticLink';

export default function HeroSection() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [typewriterText, setTypewriterText] = useState('');
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  const words = ['Intelligent', 'Powerful', 'Seamless', 'Revolutionary'];
  const fullText = 'RouKey: The Ultimate AI Gateway';

  useEffect(() => {
    // Faster typewriter effect for the main title
    let i = 0;
    const typeInterval = setInterval(() => {
      if (i < fullText.length) {
        setTypewriterText(fullText.slice(0, i + 1));
        i++;
      } else {
        clearInterval(typeInterval);
      }
    }, 40); // Much faster typing

    // Faster rotating words effect
    const wordInterval = setInterval(() => {
      setCurrentWordIndex((prev) => (prev + 1) % words.length);
    }, 1500); // Faster word rotation

    return () => {
      clearInterval(typeInterval);
      clearInterval(wordInterval);
    };
  }, []);

  return (
    <section className="relative min-h-screen bg-white overflow-hidden">
      {/* Animated Grid Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-white"></div>
        {/* Primary Grid - Much More Visible */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 0, 0, 0.32) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 0, 0, 0.32) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite',
            mask: `
              radial-gradient(ellipse 100% 100% at center, black 20%, transparent 85%),
              linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%),
              linear-gradient(to bottom, transparent 0%, black 10%, black 90%, transparent 100%)
            `,
            maskComposite: 'intersect',
            WebkitMask: `
              radial-gradient(ellipse 100% 100% at center, black 20%, transparent 85%),
              linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%),
              linear-gradient(to bottom, transparent 0%, black 10%, black 90%, transparent 100%)
            `,
            WebkitMaskComposite: 'source-in'
          }}
        ></div>

        {/* Secondary Orange Grid - More Visible */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.24) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.24) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px',
            animation: 'grid-move 25s linear infinite reverse',
            mask: `
              radial-gradient(ellipse 80% 80% at center, black 30%, transparent 80%),
              linear-gradient(to right, transparent 5%, black 15%, black 85%, transparent 95%),
              linear-gradient(to bottom, transparent 5%, black 15%, black 85%, transparent 95%)
            `,
            maskComposite: 'intersect',
            WebkitMask: `
              radial-gradient(ellipse 80% 80% at center, black 30%, transparent 80%),
              linear-gradient(to right, transparent 5%, black 15%, black 85%, transparent 95%),
              linear-gradient(to bottom, transparent 5%, black 15%, black 85%, transparent 95%)
            `,
            WebkitMaskComposite: 'source-in'
          }}
        ></div>

        {/* Accent Grid Dots */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle, rgba(255, 107, 53, 0.48) 1px, transparent 1px)`,
            backgroundSize: '100px 100px',
            animation: 'grid-move 30s linear infinite',
            mask: `radial-gradient(ellipse 60% 60% at center, black 40%, transparent 70%)`,
            WebkitMask: `radial-gradient(ellipse 60% 60% at center, black 40%, transparent 70%)`
          }}
        ></div>

        {/* Smoky overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-white via-transparent to-white opacity-60"></div>

        {/* Floating orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-[#ff6b35]/10 to-[#ff6b35]/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-[#ff6b35]/5 to-[#ff6b35]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
            className="text-left"
          >
            {/* Typewriter Title */}
            <div className="mb-8">
              <h1 className="text-5xl md:text-6xl font-bold text-black mb-4 leading-tight max-w-6xl">
                {typewriterText}
                <span className="animate-pulse text-[#ff6b35]">|</span>
              </h1>

              <div className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 max-w-6xl">
                <span className="text-gray-700">Make AI </span>
                <span
                  className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] transition-all duration-500"
                  key={currentWordIndex}
                >
                  {words[currentWordIndex]}
                </span>
              </div>
            </div>

            <p className="text-xl md:text-2xl text-gray-700 mb-12 leading-relaxed max-w-3xl">
              <span className="text-[#ff6b35] font-semibold">Unlimited requests</span> to{' '}
              <span className="text-[#ff6b35] font-semibold">300+ AI models</span>.
              Zero configuration, maximum performance.
            </p>

            {/* Feature highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <BoltIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-700 font-medium">Lightning Fast</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <ShieldCheckIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-700 font-medium">Enterprise Ready</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-[#ff6b35]/20 rounded-lg">
                  <SparklesIcon className="h-6 w-6 text-[#ff6b35]" />
                </div>
                <span className="text-gray-700 font-medium">AI-Powered</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-6"
            >
              <OptimisticLink
                href="/auth/signup"
                className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-bold rounded-xl hover:shadow-2xl hover:shadow-orange-500/25 hover:scale-105 transition-all duration-200 text-lg relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-[#f7931e] to-[#ff6b35] opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                <span className="relative z-10">Start Building Now</span>
                <ArrowRightIcon className="ml-3 h-5 w-5 relative z-10 group-hover:translate-x-1 transition-transform duration-200" />
              </OptimisticLink>

              <button
                onClick={() => setIsVideoPlaying(true)}
                className="inline-flex items-center px-8 py-4 border-2 border-black text-black font-bold rounded-xl hover:border-[#ff6b35] hover:text-[#ff6b35] hover:bg-[#ff6b35]/10 transition-all duration-300 text-lg group"
              >
                <PlayIcon className="mr-3 h-5 w-5 group-hover:text-[#ff6b35] transition-colors duration-300" />
                Watch Demo
              </button>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="flex flex-col sm:flex-row gap-8 mt-12"
            >
              <div className="text-left">
                <div className="text-3xl font-bold text-black">300+</div>
                <div className="text-gray-600">AI Models</div>
              </div>
              <div className="text-left">
                <div className="text-3xl font-bold text-black">99.9%</div>
                <div className="text-gray-600">Uptime</div>
              </div>
              <div className="text-left">
                <div className="text-3xl font-bold text-black">&lt;500ms</div>
                <div className="text-gray-600">Response Time</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Premium Visual */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className="relative lg:-mt-16"
          >
            {/* AI Routing Dashboard */}
            <div className="relative bg-black/90 rounded-xl shadow-2xl border border-[#ff6b35]/30 overflow-hidden backdrop-blur-sm">
              {/* Grid Background */}
              <div className="absolute inset-0 opacity-10">
                <div className="w-full h-full" style={{
                  backgroundImage: `
                    linear-gradient(rgba(255, 107, 53, 0.2) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.2) 1px, transparent 1px)
                  `,
                  backgroundSize: '20px 20px'
                }}></div>
              </div>

              <div className="relative z-10 p-4">
                {/* Header */}
                <div className="text-center mb-4">
                  <div className="inline-flex items-center px-3 py-1.5 bg-black rounded-lg border border-[#ff6b35]/50 mb-2">
                    <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-pulse mr-2"></div>
                    <span className="text-white text-xs font-medium">Intelligent Routing Active</span>
                  </div>
                  <div className="text-gray-400 text-xs">Request: "Best AI for coding"</div>
                </div>

                {/* Central RouKey Hub */}
                <div className="flex justify-center mb-4">
                  <div className="relative">
                    <div className="w-20 h-20 bg-white rounded-lg flex items-center justify-center shadow-xl p-1">
                      <Image
                        src="/roukey_logo.png"
                        alt="RouKey"
                        width={72}
                        height={72}
                        className="w-full h-full object-contain"
                        priority
                      />
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-[#ff6b35] rounded-full animate-pulse"></div>
                    <div className="text-center mt-2">
                      <div className="text-white font-bold text-sm">RouKey</div>
                      <div className="text-gray-400 text-xs">Intelligent Router</div>
                    </div>
                  </div>
                </div>

                {/* Provider Grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-4">
                  {/* OpenAI */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-black border border-white/20 rounded-lg p-2 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-2">
                        <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center p-0">
                          <Image
                            src="/openai_logo.jpg"
                            alt="OpenAI"
                            width={40}
                            height={40}
                            className="w-full h-full object-cover rounded-md"
                          />
                        </div>
                        <div className="w-3 h-3 bg-white/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium text-xs mb-1">OpenAI</div>
                      <div className="text-gray-400 text-xs">AVAILABLE</div>
                    </div>
                  </div>

                  {/* Anthropic - Selected */}
                  <div className="relative group">
                    <div className="bg-black border-2 border-[#ff6b35] rounded-lg p-2 shadow-lg shadow-[#ff6b35]/25 transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-2">
                        <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center p-0">
                          <Image
                            src="/claude_logo.png"
                            alt="Anthropic"
                            width={40}
                            height={40}
                            className="w-full h-full object-cover rounded-md"
                          />
                        </div>
                        <div className="w-3 h-3 bg-[#ff6b35] rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      </div>
                      <div className="text-white font-medium text-xs mb-1">Anthropic</div>
                      <div className="text-[#ff6b35] text-xs font-medium">SELECTED</div>
                    </div>
                  </div>

                  {/* Google */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-black border border-white/20 rounded-lg p-2 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-2">
                        <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center p-0">
                          <Image
                            src="/gemini_logo.png"
                            alt="Google Gemini"
                            width={40}
                            height={40}
                            className="w-full h-full object-cover rounded-md"
                          />
                        </div>
                        <div className="w-3 h-3 bg-white/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium text-xs mb-1">Google</div>
                      <div className="text-gray-400 text-xs">AVAILABLE</div>
                    </div>
                  </div>

                  {/* Deepseek */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-black border border-white/20 rounded-lg p-2 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-2">
                        <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center p-0">
                          <Image
                            src="/deepseek_logo.png"
                            alt="Deepseek"
                            width={40}
                            height={40}
                            className="w-full h-full object-cover rounded-md"
                          />
                        </div>
                        <div className="w-3 h-3 bg-white/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium text-xs mb-1">Deepseek</div>
                      <div className="text-gray-400 text-xs">AVAILABLE</div>
                    </div>
                  </div>

                  {/* Mistral */}
                  <div className="relative group opacity-70 hover:opacity-100 transition-opacity">
                    <div className="bg-black border border-white/20 rounded-lg p-2 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-2">
                        <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center p-0.5">
                          <Image
                            src="/mistral_logo.png"
                            alt="Mistral"
                            width={36}
                            height={36}
                            className="object-cover"
                          />
                        </div>
                        <div className="w-3 h-3 bg-white/50 rounded-full animate-pulse"></div>
                      </div>
                      <div className="text-white font-medium text-xs mb-1">Mistral</div>
                      <div className="text-gray-400 text-xs">AVAILABLE</div>
                    </div>
                  </div>

                  {/* More Providers */}
                  <div className="relative group opacity-50 hover:opacity-70 transition-opacity">
                    <div className="bg-black border border-white/20 border-dashed rounded-lg p-2 shadow-lg transform transition-all duration-300 hover:scale-105">
                      <div className="flex items-center justify-between mb-2">
                        <div className="w-10 h-10 bg-white/10 rounded-md flex items-center justify-center">
                          <span className="text-white font-bold text-sm">+</span>
                        </div>
                        <div className="w-3 h-3 bg-white/30 rounded-full"></div>
                      </div>
                      <div className="text-white font-medium text-xs mb-1">300+ Models</div>
                      <div className="text-gray-400 text-xs">AVAILABLE</div>
                    </div>
                  </div>
                </div>

                {/* Status Bar */}
                <div className="flex items-center justify-between bg-black/50 rounded-lg p-3 border border-[#ff6b35]/30">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-pulse mr-2"></div>
                    <span className="text-white text-xs font-medium">Best model selected automatically</span>
                  </div>
                  <div className="text-right">
                    <div className="text-[#ff6b35] text-sm font-bold">$1,247</div>
                    <div className="text-gray-400 text-xs">Cost Saved</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Premium Floating elements */}
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="absolute -top-3 -right-3 bg-black border border-[#ff6b35]/50 rounded-lg shadow-2xl p-3 backdrop-blur-sm"
            >
              <div className="text-xs text-gray-400 mb-1">Cost Saved</div>
              <div className="text-lg font-bold text-[#ff6b35]">$1,247</div>
            </motion.div>

            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 4, repeat: Infinity, delay: 1 }}
              className="absolute -bottom-3 -left-3 bg-black border border-[#ff6b35]/50 rounded-lg shadow-2xl p-3 backdrop-blur-sm"
            >
              <div className="text-xs text-gray-400 mb-1">Requests Today</div>
              <div className="text-lg font-bold text-[#ff6b35]">2,847</div>
            </motion.div>


          </motion.div>
        </div>
      </div>

      {/* Add CSS for grid animation */}
      <style jsx>{`
        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(50px, 50px); }
        }
      `}</style>
    </section>
  );
}
