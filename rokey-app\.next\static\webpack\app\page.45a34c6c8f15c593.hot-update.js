"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx":
/*!********************************************************!*\
  !*** ./src/components/landing/TestimonialsSection.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"Lead Developer\",\n        company: \"TechFlow Inc\",\n        avatar: \"SC\",\n        image: \"/sarah.jpg\",\n        content: \"RouKey has revolutionized how we handle AI requests. The intelligent routing saves us hours of manual configuration, and the cost optimization features have reduced our AI spending by 40%.\",\n        rating: 5\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"CTO\",\n        company: \"DataVision Labs\",\n        avatar: \"MR\",\n        image: \"/marcus.jpg\",\n        content: \"The automatic failover feature is a game-changer. We never worry about API limits or downtime anymore. RouKey just handles everything seamlessly in the background.\",\n        rating: 5\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"AI Engineer\",\n        company: \"InnovateCorp\",\n        avatar: \"EW\",\n        image: \"/Emily.jpg\",\n        content: \"Having access to 300+ models through one API is incredible. The performance analytics help us optimize our model selection, and the security features give us peace of mind.\",\n        rating: 5\n    },\n    {\n        name: \"David Kim\",\n        role: \"Product Manager\",\n        company: \"StartupXYZ\",\n        avatar: \"DK\",\n        image: \"/David.jpg\",\n        content: \"RouKey's intelligent routing is like having an AI expert on our team. It automatically picks the best model for each task, improving our output quality significantly.\",\n        rating: 5\n    },\n    {\n        name: \"Lisa Thompson\",\n        role: \"Engineering Manager\",\n        company: \"ScaleUp Solutions\",\n        avatar: \"LT\",\n        image: \"/Lisa.jpg\",\n        content: \"The comprehensive analytics and cost tracking features are outstanding. We can now make data-driven decisions about our AI infrastructure and optimize our spending.\",\n        rating: 5\n    },\n    {\n        name: \"Alex Johnson\",\n        role: \"Senior Developer\",\n        company: \"CloudTech Pro\",\n        avatar: \"AJ\",\n        image: \"/Alex.jpg\",\n        content: \"RouKey's enterprise security and reliability features make it perfect for production environments. The team management features are exactly what we needed.\",\n        rating: 5\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Trusted by Developers Worldwide\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"See what developers and teams are saying about RouKey's intelligent AI routing platform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        ...Array(testimonial.rating)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#ff6b35]\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: testimonial.image,\n                                                alt: \"\".concat(testimonial.name, \" profile picture\"),\n                                                width: 48,\n                                                height: 48,\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: testimonial.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        testimonial.role,\n                                                        \" at \",\n                                                        testimonial.company\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, testimonial.name, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\n"));

/***/ })

});