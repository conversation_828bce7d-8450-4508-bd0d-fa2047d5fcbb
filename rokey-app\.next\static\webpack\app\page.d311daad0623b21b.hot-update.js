"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/serviceWorker */ \"(app-pages-browser)/./src/utils/serviceWorker.ts\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/HeroSection */ \"(app-pages-browser)/./src/components/landing/HeroSection.tsx\");\n/* harmony import */ var _components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/TrustBadges */ \"(app-pages-browser)/./src/components/landing/TrustBadges.tsx\");\n/* harmony import */ var _components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/landing/FeaturesSection */ \"(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\");\n/* harmony import */ var _components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/landing/RoutingVisualization */ \"(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\");\n/* harmony import */ var _components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/landing/TestimonialsSection */ \"(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\");\n/* harmony import */ var _components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/CTASection */ \"(app-pages-browser)/./src/components/landing/CTASection.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            // Register service worker for caching\n            (0,_utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__.registerServiceWorker)({\n                onSuccess: {\n                    \"LandingPage.useEffect\": ()=>console.log('✅ Service Worker registered for caching')\n                }[\"LandingPage.useEffect\"],\n                onUpdate: {\n                    \"LandingPage.useEffect\": ()=>console.log('🔄 New content available')\n                }[\"LandingPage.useEffect\"],\n                onError: {\n                    \"LandingPage.useEffect\": (error)=>console.warn('⚠️ Service Worker registration failed:', error)\n                }[\"LandingPage.useEffect\"]\n            });\n            setIsLoaded(true);\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Prefetch likely next pages on hover\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (true) {\n                const prefetchPages = {\n                    \"LandingPage.useEffect.prefetchPages\": ()=>{\n                        // Prefetch auth pages since users likely to sign up\n                        const link1 = document.createElement('link');\n                        link1.rel = 'prefetch';\n                        link1.href = '/pricing';\n                        document.head.appendChild(link1);\n                        const link2 = document.createElement('link');\n                        link2.rel = 'prefetch';\n                        link2.href = '/auth/signup';\n                        document.head.appendChild(link2);\n                        const link3 = document.createElement('link');\n                        link3.rel = 'prefetch';\n                        link3.href = '/features';\n                        document.head.appendChild(link3);\n                    }\n                }[\"LandingPage.useEffect.prefetchPages\"];\n                // Prefetch after initial load\n                const prefetchTimer = setTimeout(prefetchPages, 1000);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearTimeout(prefetchTimer)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], []);\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black text-sm\",\n                        children: \"Loading RouKey...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"/3xRDC4vi9aEd7HfgSAcZoEuTKU=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});