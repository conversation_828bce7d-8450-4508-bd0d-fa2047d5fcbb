"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx":
/*!********************************************************!*\
  !*** ./src/components/landing/TestimonialsSection.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"Lead Developer\",\n        company: \"TechFlow Inc\",\n        avatar: \"SC\",\n        image: \"/sarah.jpg\",\n        content: \"RouKey has revolutionized how we handle AI requests. The intelligent routing saves us hours of manual configuration, and the cost optimization features have reduced our AI spending by 40%.\",\n        rating: 5\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"CTO\",\n        company: \"DataVision Labs\",\n        avatar: \"MR\",\n        image: \"/marcus.jpg\",\n        content: \"The automatic failover feature is a game-changer. We never worry about API limits or downtime anymore. RouKey just handles everything seamlessly in the background.\",\n        rating: 5\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"AI Engineer\",\n        company: \"InnovateCorp\",\n        avatar: \"EW\",\n        image: \"/Emily.jpg\",\n        content: \"Having access to 300+ models through one API is incredible. The performance analytics help us optimize our model selection, and the security features give us peace of mind.\",\n        rating: 5\n    },\n    {\n        name: \"David Kim\",\n        role: \"Product Manager\",\n        company: \"StartupXYZ\",\n        avatar: \"DK\",\n        image: \"/David.jpg\",\n        content: \"RouKey's intelligent routing is like having an AI expert on our team. It automatically picks the best model for each task, improving our output quality significantly.\",\n        rating: 5\n    },\n    {\n        name: \"Lisa Thompson\",\n        role: \"Engineering Manager\",\n        company: \"ScaleUp Solutions\",\n        avatar: \"LT\",\n        image: \"/Lisa.jpg\",\n        content: \"The comprehensive analytics and cost tracking features are outstanding. We can now make data-driven decisions about our AI infrastructure and optimize our spending.\",\n        rating: 5\n    },\n    {\n        name: \"Alex Johnson\",\n        role: \"Senior Developer\",\n        company: \"CloudTech Pro\",\n        avatar: \"AJ\",\n        image: \"/Alex.jpg\",\n        content: \"RouKey's enterprise security and reliability features make it perfect for production environments. The team management features are exactly what we needed.\",\n        rating: 5\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                gridSize: 50,\n                opacity: 0.05,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"absolute inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"Trusted by Developers Worldwide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"See what developers and teams are saying about RouKey's intelligent AI routing platform\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            ...Array(testimonial.rating)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-[#ff6b35]\"\n                                            }, i, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-6 leading-relaxed\",\n                                        children: [\n                                            '\"',\n                                            testimonial.content,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: testimonial.image,\n                                                    alt: \"\".concat(testimonial.name, \" profile picture\"),\n                                                    width: 48,\n                                                    height: 48,\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            testimonial.role,\n                                                            \" at \",\n                                                            testimonial.company\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, testimonial.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xhbmRpbmcvVGVzdGltb25pYWxzU2VjdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV1QztBQUNjO0FBQ3RCO0FBQytCO0FBRTlELE1BQU1JLGVBQWU7SUFDbkI7UUFDRUMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsUUFBUTtJQUNWO0lBQ0E7UUFDRU4sTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsUUFBUTtJQUNWO0lBQ0E7UUFDRU4sTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsUUFBUTtJQUNWO0lBQ0E7UUFDRU4sTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsUUFBUTtJQUNWO0lBQ0E7UUFDRU4sTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsUUFBUTtJQUNWO0lBQ0E7UUFDRU4sTUFBTTtRQUNOQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsUUFBUTtJQUNWO0NBQ0Q7QUFFYyxTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBUUMsV0FBVTs7MEJBRWpCLDhEQUFDWCwrREFBc0JBO2dCQUNyQlksVUFBVTtnQkFDVkMsU0FBUztnQkFDVEMsT0FBTTtnQkFDTkMsU0FBUTtnQkFDUkMsVUFBVTtnQkFDVkwsV0FBVTs7Ozs7OzBCQUdaLDhEQUFDTTtnQkFBSU4sV0FBVTs7a0NBRWIsOERBQUNNO3dCQUFJTixXQUFVOzswQ0FDYiw4REFBQ2QsaURBQU1BLENBQUNxQixFQUFFO2dDQUNSQyxTQUFTO29DQUFFTixTQUFTO29DQUFHTyxHQUFHO2dDQUFHO2dDQUM3QkMsYUFBYTtvQ0FBRVIsU0FBUztvQ0FBR08sR0FBRztnQ0FBRTtnQ0FDaENFLFVBQVU7b0NBQUVDLE1BQU07Z0NBQUs7Z0NBQ3ZCWixXQUFVOzBDQUNYOzs7Ozs7MENBR0QsOERBQUNkLGlEQUFNQSxDQUFDMkIsQ0FBQztnQ0FDUEwsU0FBUztvQ0FBRU4sU0FBUztvQ0FBR08sR0FBRztnQ0FBRztnQ0FDN0JDLGFBQWE7b0NBQUVSLFNBQVM7b0NBQUdPLEdBQUc7Z0NBQUU7Z0NBQ2hDRSxVQUFVO29DQUFFQyxNQUFNO2dDQUFLO2dDQUN2QkUsWUFBWTtvQ0FBRUMsT0FBTztnQ0FBSTtnQ0FDekJmLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7OztrQ0FNSCw4REFBQ007d0JBQUlOLFdBQVU7a0NBQ1pWLGFBQWEwQixHQUFHLENBQUMsQ0FBQ0MsYUFBYUMsc0JBQzlCLDhEQUFDaEMsaURBQU1BLENBQUNvQixHQUFHO2dDQUVURSxTQUFTO29DQUFFTixTQUFTO29DQUFHTyxHQUFHO2dDQUFHO2dDQUM3QkMsYUFBYTtvQ0FBRVIsU0FBUztvQ0FBR08sR0FBRztnQ0FBRTtnQ0FDaENFLFVBQVU7b0NBQUVDLE1BQU07Z0NBQUs7Z0NBQ3ZCRSxZQUFZO29DQUFFQyxPQUFPRyxRQUFRO2dDQUFJO2dDQUNqQ2xCLFdBQVU7O2tEQUdWLDhEQUFDTTt3Q0FBSU4sV0FBVTtrREFDWjsrQ0FBSW1CLE1BQU1GLFlBQVlwQixNQUFNO3lDQUFFLENBQUNtQixHQUFHLENBQUMsQ0FBQ0ksR0FBR0Msa0JBQ3RDLDhEQUFDbEMsZ0dBQVFBO2dEQUFTYSxXQUFVOytDQUFicUI7Ozs7Ozs7Ozs7a0RBS25CLDhEQUFDUjt3Q0FBRWIsV0FBVTs7NENBQXFDOzRDQUM5Q2lCLFlBQVlyQixPQUFPOzRDQUFDOzs7Ozs7O2tEQUl4Qiw4REFBQ1U7d0NBQUlOLFdBQVU7OzBEQUNiLDhEQUFDTTtnREFBSU4sV0FBVTswREFDYiw0RUFBQ1osa0RBQUtBO29EQUNKa0MsS0FBS0wsWUFBWXRCLEtBQUs7b0RBQ3RCNEIsS0FBSyxHQUFvQixPQUFqQk4sWUFBWTFCLElBQUksRUFBQztvREFDekJpQyxPQUFPO29EQUNQQyxRQUFRO29EQUNSekIsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNNOztrRUFDQyw4REFBQ0E7d0RBQUlOLFdBQVU7a0VBQStCaUIsWUFBWTFCLElBQUk7Ozs7OztrRUFDOUQsOERBQUNlO3dEQUFJTixXQUFVOzs0REFDWmlCLFlBQVl6QixJQUFJOzREQUFDOzREQUFLeUIsWUFBWXhCLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQWpDM0N3QixZQUFZMUIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQTZDbkM7S0FwRndCTyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXGxhbmRpbmdcXFRlc3RpbW9uaWFsc1NlY3Rpb24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBTdGFySWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvc29saWQnO1xuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnO1xuaW1wb3J0IEVuaGFuY2VkR3JpZEJhY2tncm91bmQgZnJvbSAnLi9FbmhhbmNlZEdyaWRCYWNrZ3JvdW5kJztcblxuY29uc3QgdGVzdGltb25pYWxzID0gW1xuICB7XG4gICAgbmFtZTogXCJTYXJhaCBDaGVuXCIsXG4gICAgcm9sZTogXCJMZWFkIERldmVsb3BlclwiLFxuICAgIGNvbXBhbnk6IFwiVGVjaEZsb3cgSW5jXCIsXG4gICAgYXZhdGFyOiBcIlNDXCIsXG4gICAgaW1hZ2U6IFwiL3NhcmFoLmpwZ1wiLFxuICAgIGNvbnRlbnQ6IFwiUm91S2V5IGhhcyByZXZvbHV0aW9uaXplZCBob3cgd2UgaGFuZGxlIEFJIHJlcXVlc3RzLiBUaGUgaW50ZWxsaWdlbnQgcm91dGluZyBzYXZlcyB1cyBob3VycyBvZiBtYW51YWwgY29uZmlndXJhdGlvbiwgYW5kIHRoZSBjb3N0IG9wdGltaXphdGlvbiBmZWF0dXJlcyBoYXZlIHJlZHVjZWQgb3VyIEFJIHNwZW5kaW5nIGJ5IDQwJS5cIixcbiAgICByYXRpbmc6IDVcbiAgfSxcbiAge1xuICAgIG5hbWU6IFwiTWFyY3VzIFJvZHJpZ3VlelwiLFxuICAgIHJvbGU6IFwiQ1RPXCIsXG4gICAgY29tcGFueTogXCJEYXRhVmlzaW9uIExhYnNcIixcbiAgICBhdmF0YXI6IFwiTVJcIixcbiAgICBpbWFnZTogXCIvbWFyY3VzLmpwZ1wiLFxuICAgIGNvbnRlbnQ6IFwiVGhlIGF1dG9tYXRpYyBmYWlsb3ZlciBmZWF0dXJlIGlzIGEgZ2FtZS1jaGFuZ2VyLiBXZSBuZXZlciB3b3JyeSBhYm91dCBBUEkgbGltaXRzIG9yIGRvd250aW1lIGFueW1vcmUuIFJvdUtleSBqdXN0IGhhbmRsZXMgZXZlcnl0aGluZyBzZWFtbGVzc2x5IGluIHRoZSBiYWNrZ3JvdW5kLlwiLFxuICAgIHJhdGluZzogNVxuICB9LFxuICB7XG4gICAgbmFtZTogXCJFbWlseSBXYXRzb25cIixcbiAgICByb2xlOiBcIkFJIEVuZ2luZWVyXCIsXG4gICAgY29tcGFueTogXCJJbm5vdmF0ZUNvcnBcIixcbiAgICBhdmF0YXI6IFwiRVdcIixcbiAgICBpbWFnZTogXCIvRW1pbHkuanBnXCIsXG4gICAgY29udGVudDogXCJIYXZpbmcgYWNjZXNzIHRvIDMwMCsgbW9kZWxzIHRocm91Z2ggb25lIEFQSSBpcyBpbmNyZWRpYmxlLiBUaGUgcGVyZm9ybWFuY2UgYW5hbHl0aWNzIGhlbHAgdXMgb3B0aW1pemUgb3VyIG1vZGVsIHNlbGVjdGlvbiwgYW5kIHRoZSBzZWN1cml0eSBmZWF0dXJlcyBnaXZlIHVzIHBlYWNlIG9mIG1pbmQuXCIsXG4gICAgcmF0aW5nOiA1XG4gIH0sXG4gIHtcbiAgICBuYW1lOiBcIkRhdmlkIEtpbVwiLFxuICAgIHJvbGU6IFwiUHJvZHVjdCBNYW5hZ2VyXCIsXG4gICAgY29tcGFueTogXCJTdGFydHVwWFlaXCIsXG4gICAgYXZhdGFyOiBcIkRLXCIsXG4gICAgaW1hZ2U6IFwiL0RhdmlkLmpwZ1wiLFxuICAgIGNvbnRlbnQ6IFwiUm91S2V5J3MgaW50ZWxsaWdlbnQgcm91dGluZyBpcyBsaWtlIGhhdmluZyBhbiBBSSBleHBlcnQgb24gb3VyIHRlYW0uIEl0IGF1dG9tYXRpY2FsbHkgcGlja3MgdGhlIGJlc3QgbW9kZWwgZm9yIGVhY2ggdGFzaywgaW1wcm92aW5nIG91ciBvdXRwdXQgcXVhbGl0eSBzaWduaWZpY2FudGx5LlwiLFxuICAgIHJhdGluZzogNVxuICB9LFxuICB7XG4gICAgbmFtZTogXCJMaXNhIFRob21wc29uXCIsXG4gICAgcm9sZTogXCJFbmdpbmVlcmluZyBNYW5hZ2VyXCIsXG4gICAgY29tcGFueTogXCJTY2FsZVVwIFNvbHV0aW9uc1wiLFxuICAgIGF2YXRhcjogXCJMVFwiLFxuICAgIGltYWdlOiBcIi9MaXNhLmpwZ1wiLFxuICAgIGNvbnRlbnQ6IFwiVGhlIGNvbXByZWhlbnNpdmUgYW5hbHl0aWNzIGFuZCBjb3N0IHRyYWNraW5nIGZlYXR1cmVzIGFyZSBvdXRzdGFuZGluZy4gV2UgY2FuIG5vdyBtYWtlIGRhdGEtZHJpdmVuIGRlY2lzaW9ucyBhYm91dCBvdXIgQUkgaW5mcmFzdHJ1Y3R1cmUgYW5kIG9wdGltaXplIG91ciBzcGVuZGluZy5cIixcbiAgICByYXRpbmc6IDVcbiAgfSxcbiAge1xuICAgIG5hbWU6IFwiQWxleCBKb2huc29uXCIsXG4gICAgcm9sZTogXCJTZW5pb3IgRGV2ZWxvcGVyXCIsXG4gICAgY29tcGFueTogXCJDbG91ZFRlY2ggUHJvXCIsXG4gICAgYXZhdGFyOiBcIkFKXCIsXG4gICAgaW1hZ2U6IFwiL0FsZXguanBnXCIsXG4gICAgY29udGVudDogXCJSb3VLZXkncyBlbnRlcnByaXNlIHNlY3VyaXR5IGFuZCByZWxpYWJpbGl0eSBmZWF0dXJlcyBtYWtlIGl0IHBlcmZlY3QgZm9yIHByb2R1Y3Rpb24gZW52aXJvbm1lbnRzLiBUaGUgdGVhbSBtYW5hZ2VtZW50IGZlYXR1cmVzIGFyZSBleGFjdGx5IHdoYXQgd2UgbmVlZGVkLlwiLFxuICAgIHJhdGluZzogNVxuICB9XG5dO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUZXN0aW1vbmlhbHNTZWN0aW9uKCkge1xuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLWdyYXktNTAgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICB7LyogRW5oYW5jZWQgR3JpZCBCYWNrZ3JvdW5kICovfVxuICAgICAgPEVuaGFuY2VkR3JpZEJhY2tncm91bmRcbiAgICAgICAgZ3JpZFNpemU9ezUwfVxuICAgICAgICBvcGFjaXR5PXswLjA1fVxuICAgICAgICBjb2xvcj1cIiMwMDAwMDBcIlxuICAgICAgICB2YXJpYW50PVwic3VidGxlXCJcbiAgICAgICAgYW5pbWF0ZWQ9e3RydWV9XG4gICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTBcIlxuICAgICAgLz5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCByZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgIHsvKiBTZWN0aW9uIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xNlwiPlxuICAgICAgICAgIDxtb3Rpb24uaDJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtM3hsIHNtOnRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIFRydXN0ZWQgYnkgRGV2ZWxvcGVycyBXb3JsZHdpZGVcbiAgICAgICAgICA8L21vdGlvbi5oMj5cbiAgICAgICAgICA8bW90aW9uLnBcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwIG1heC13LTN4bCBteC1hdXRvXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBTZWUgd2hhdCBkZXZlbG9wZXJzIGFuZCB0ZWFtcyBhcmUgc2F5aW5nIGFib3V0IFJvdUtleSdzIGludGVsbGlnZW50IEFJIHJvdXRpbmcgcGxhdGZvcm1cbiAgICAgICAgICA8L21vdGlvbi5wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogVGVzdGltb25pYWxzIEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgIHt0ZXN0aW1vbmlhbHMubWFwKCh0ZXN0aW1vbmlhbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgIGtleT17dGVzdGltb25pYWwubmFtZX1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBwLTYgc2hhZG93LXNtIGJvcmRlciBib3JkZXItZ3JheS0yMDAgaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tc2hhZG93IGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHsvKiBSYXRpbmcgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgIHtbLi4uQXJyYXkodGVzdGltb25pYWwucmF0aW5nKV0ubWFwKChfLCBpKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8U3Rhckljb24ga2V5PXtpfSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtWyNmZjZiMzVdXCIgLz5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgbWItNiBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICBcInt0ZXN0aW1vbmlhbC5jb250ZW50fVwiXG4gICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICB7LyogQXV0aG9yICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgcm91bmRlZC1mdWxsIG92ZXJmbG93LWhpZGRlbiBtci00IGJvcmRlci0yIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgIHNyYz17dGVzdGltb25pYWwuaW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgIGFsdD17YCR7dGVzdGltb25pYWwubmFtZX0gcHJvZmlsZSBwaWN0dXJlYH1cbiAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezQ4fVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezQ4fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPnt0ZXN0aW1vbmlhbC5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge3Rlc3RpbW9uaWFsLnJvbGV9IGF0IHt0ZXN0aW1vbmlhbC5jb21wYW55fVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuXG4gICAgICA8L2Rpdj5cbiAgICA8L3NlY3Rpb24+XG4gICk7XG59XG4iXSwibmFtZXMiOlsibW90aW9uIiwiU3Rhckljb24iLCJJbWFnZSIsIkVuaGFuY2VkR3JpZEJhY2tncm91bmQiLCJ0ZXN0aW1vbmlhbHMiLCJuYW1lIiwicm9sZSIsImNvbXBhbnkiLCJhdmF0YXIiLCJpbWFnZSIsImNvbnRlbnQiLCJyYXRpbmciLCJUZXN0aW1vbmlhbHNTZWN0aW9uIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImdyaWRTaXplIiwib3BhY2l0eSIsImNvbG9yIiwidmFyaWFudCIsImFuaW1hdGVkIiwiZGl2IiwiaDIiLCJpbml0aWFsIiwieSIsIndoaWxlSW5WaWV3Iiwidmlld3BvcnQiLCJvbmNlIiwicCIsInRyYW5zaXRpb24iLCJkZWxheSIsIm1hcCIsInRlc3RpbW9uaWFsIiwiaW5kZXgiLCJBcnJheSIsIl8iLCJpIiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\n"));

/***/ })

});