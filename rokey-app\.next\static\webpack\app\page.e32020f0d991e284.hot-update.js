"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx":
/*!***********************************************************!*\
  !*** ./src/components/landing/EnhancedGridBackground.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EnhancedGridBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction EnhancedGridBackground(param) {\n    let { className = '', gridSize = 40, opacity = 0.1, color = '#000000', animated = false, glowEffect = false, variant = 'subtle' } = param;\n    const getVariantStyles = ()=>{\n        // Convert color to rgba format for proper opacity handling\n        const getRgbaColor = (baseColor, alpha)=>{\n            if (baseColor === '#000000') {\n                return \"rgba(0, 0, 0, \".concat(alpha, \")\");\n            } else if (baseColor === '#ffffff') {\n                return \"rgba(255, 255, 255, \".concat(alpha, \")\");\n            } else if (baseColor === '#ff6b35') {\n                return \"rgba(255, 107, 53, \".concat(alpha, \")\");\n            } else {\n                // Fallback for other colors\n                return \"\".concat(baseColor).concat(Math.round(alpha * 255).toString(16).padStart(2, '0'));\n            }\n        };\n        // Enhanced opacity for much more visible grids\n        const enhancedOpacity = opacity * 4; // Make it 4x more visible\n        switch(variant){\n            case 'tech':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px),\\n            radial-gradient(circle at 50% 50%, \").concat(getRgbaColor(color, enhancedOpacity * 0.5), \" 2px, transparent 2px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 4, \"px \").concat(gridSize * 4, \"px\"),\n                    animation: animated ? 'tech-grid-move 30s linear infinite' : 'none',\n                    mask: \"\\n            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),\\n            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)\\n          \",\n                    maskComposite: 'intersect',\n                    WebkitMask: \"\\n            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),\\n            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)\\n          \",\n                    WebkitMaskComposite: 'source-in'\n                };\n            case 'premium':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, enhancedOpacity), \" 0.5px, transparent 0.5px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity), \" 0.5px, transparent 0.5px),\\n            linear-gradient(\").concat(getRgbaColor(color, enhancedOpacity * 0.7), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity * 0.7), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px\"),\n                    animation: animated ? 'premium-grid-float 40s ease-in-out infinite' : 'none',\n                    mask: \"\\n            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),\\n            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),\\n            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)\\n          \",\n                    maskComposite: 'intersect',\n                    WebkitMask: \"\\n            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),\\n            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),\\n            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)\\n          \",\n                    WebkitMaskComposite: 'source-in'\n                };\n            default:\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, enhancedOpacity), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px\"),\n                    animation: animated ? 'subtle-grid-drift 25s linear infinite' : 'none',\n                    mask: \"\\n            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),\\n            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)\\n          \",\n                    maskComposite: 'intersect',\n                    WebkitMask: \"\\n            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),\\n            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),\\n            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)\\n          \",\n                    WebkitMaskComposite: 'source-in'\n                };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...getVariantStyles(),\n                    zIndex: 1,\n                    filter: glowEffect ? 'drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))' : 'none'\n                },\n                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                    [\n                        \"cdf0235daf430a20\",\n                        [\n                            gridSize,\n                            gridSize,\n                            gridSize * 0.5,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.7,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1\n                        ]\n                    ]\n                ]) + \" \" + \"absolute inset-0 pointer-events-none \".concat(className)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\EnhancedGridBackground.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"cdf0235daf430a20\",\n                dynamic: [\n                    gridSize,\n                    gridSize,\n                    gridSize * 0.5,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.7,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1\n                ],\n                children: \"@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\".concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_c = EnhancedGridBackground;\nvar _c;\n$RefreshReg$(_c, \"EnhancedGridBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\n"));

/***/ })

});