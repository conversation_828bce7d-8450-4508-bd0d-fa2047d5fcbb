"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx":
/*!********************************************************!*\
  !*** ./src/components/landing/TestimonialsSection.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"Lead Developer\",\n        company: \"TechFlow Inc\",\n        avatar: \"SC\",\n        image: \"/sarah.jpg\",\n        content: \"RouKey has revolutionized how we handle AI requests. The intelligent routing saves us hours of manual configuration, and the cost optimization features have reduced our AI spending by 40%.\",\n        rating: 5\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"CTO\",\n        company: \"DataVision Labs\",\n        avatar: \"MR\",\n        image: \"/marcus.jpg\",\n        content: \"The automatic failover feature is a game-changer. We never worry about API limits or downtime anymore. RouKey just handles everything seamlessly in the background.\",\n        rating: 5\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"AI Engineer\",\n        company: \"InnovateCorp\",\n        avatar: \"EW\",\n        image: \"/Emily.jpg\",\n        content: \"Having access to 300+ models through one API is incredible. The performance analytics help us optimize our model selection, and the security features give us peace of mind.\",\n        rating: 5\n    },\n    {\n        name: \"David Kim\",\n        role: \"Product Manager\",\n        company: \"StartupXYZ\",\n        avatar: \"DK\",\n        image: \"/David.jpg\",\n        content: \"RouKey's intelligent routing is like having an AI expert on our team. It automatically picks the best model for each task, improving our output quality significantly.\",\n        rating: 5\n    },\n    {\n        name: \"Lisa Thompson\",\n        role: \"Engineering Manager\",\n        company: \"ScaleUp Solutions\",\n        avatar: \"LT\",\n        image: \"/Lisa.jpg\",\n        content: \"The comprehensive analytics and cost tracking features are outstanding. We can now make data-driven decisions about our AI infrastructure and optimize our spending.\",\n        rating: 5\n    },\n    {\n        name: \"Alex Johnson\",\n        role: \"Senior Developer\",\n        company: \"CloudTech Pro\",\n        avatar: \"AJ\",\n        image: \"/Alex.jpg\",\n        content: \"RouKey's enterprise security and reliability features make it perfect for production environments. The team management features are exactly what we needed.\",\n        rating: 5\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Trusted by Developers Worldwide\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"See what developers and teams are saying about RouKey's intelligent AI routing platform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        ...Array(testimonial.rating)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#ff6b35]\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-full flex items-center justify-center text-white font-semibold mr-4\",\n                                            children: testimonial.avatar\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: testimonial.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        testimonial.role,\n                                                        \" at \",\n                                                        testimonial.company\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, testimonial.name, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\n"));

/***/ })

});