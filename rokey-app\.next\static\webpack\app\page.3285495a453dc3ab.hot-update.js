"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx":
/*!********************************************************!*\
  !*** ./src/components/landing/TestimonialsSection.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"Lead Developer\",\n        company: \"TechFlow Inc\",\n        avatar: \"SC\",\n        image: \"/sarah.jpg\",\n        content: \"RouKey has revolutionized how we handle AI requests. The intelligent routing saves us hours of manual configuration, and the cost optimization features have reduced our AI spending by 40%.\",\n        rating: 5\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"CTO\",\n        company: \"DataVision Labs\",\n        avatar: \"MR\",\n        image: \"/marcus.jpg\",\n        content: \"The automatic failover feature is a game-changer. We never worry about API limits or downtime anymore. RouKey just handles everything seamlessly in the background.\",\n        rating: 5\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"AI Engineer\",\n        company: \"InnovateCorp\",\n        avatar: \"EW\",\n        image: \"/Emily.jpg\",\n        content: \"Having access to 300+ models through one API is incredible. The performance analytics help us optimize our model selection, and the security features give us peace of mind.\",\n        rating: 5\n    },\n    {\n        name: \"David Kim\",\n        role: \"Product Manager\",\n        company: \"StartupXYZ\",\n        avatar: \"DK\",\n        image: \"/David.jpg\",\n        content: \"RouKey's intelligent routing is like having an AI expert on our team. It automatically picks the best model for each task, improving our output quality significantly.\",\n        rating: 5\n    },\n    {\n        name: \"Lisa Thompson\",\n        role: \"Engineering Manager\",\n        company: \"ScaleUp Solutions\",\n        avatar: \"LT\",\n        image: \"/Lisa.jpg\",\n        content: \"The comprehensive analytics and cost tracking features are outstanding. We can now make data-driven decisions about our AI infrastructure and optimize our spending.\",\n        rating: 5\n    },\n    {\n        name: \"Alex Johnson\",\n        role: \"Senior Developer\",\n        company: \"CloudTech Pro\",\n        avatar: \"AJ\",\n        image: \"/Alex.jpg\",\n        content: \"RouKey's enterprise security and reliability features make it perfect for production environments. The team management features are exactly what we needed.\",\n        rating: 5\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Trusted by Developers Worldwide\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"See what developers and teams are saying about RouKey's intelligent AI routing platform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        ...Array(testimonial.rating)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#ff6b35]\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-full overflow-hidden mr-4 border-2 border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: testimonial.image,\n                                                alt: \"\".concat(testimonial.name, \" profile picture\"),\n                                                width: 48,\n                                                height: 48,\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: testimonial.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        testimonial.role,\n                                                        \" at \",\n                                                        testimonial.company\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, testimonial.name, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\n"));

/***/ })

});