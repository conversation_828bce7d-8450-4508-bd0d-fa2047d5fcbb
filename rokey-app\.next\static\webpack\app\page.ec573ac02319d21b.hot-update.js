"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/serviceWorker */ \"(app-pages-browser)/./src/utils/serviceWorker.ts\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/HeroSection */ \"(app-pages-browser)/./src/components/landing/HeroSection.tsx\");\n/* harmony import */ var _components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/TrustBadges */ \"(app-pages-browser)/./src/components/landing/TrustBadges.tsx\");\n/* harmony import */ var _components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/landing/FeaturesSection */ \"(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\");\n/* harmony import */ var _components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/landing/RoutingVisualization */ \"(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\");\n/* harmony import */ var _components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/landing/TestimonialsSection */ \"(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\");\n/* harmony import */ var _components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/CTASection */ \"(app-pages-browser)/./src/components/landing/CTASection.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            // Register service worker for caching\n            (0,_utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__.registerServiceWorker)({\n                onSuccess: {\n                    \"LandingPage.useEffect\": ()=>console.log('✅ Service Worker registered for caching')\n                }[\"LandingPage.useEffect\"],\n                onUpdate: {\n                    \"LandingPage.useEffect\": ()=>console.log('🔄 New content available')\n                }[\"LandingPage.useEffect\"],\n                onError: {\n                    \"LandingPage.useEffect\": (error)=>console.warn('⚠️ Service Worker registration failed:', error)\n                }[\"LandingPage.useEffect\"]\n            });\n            setIsLoaded(true);\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Prefetch likely next pages on hover\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (true) {\n                const prefetchPages = {\n                    \"LandingPage.useEffect.prefetchPages\": ()=>{\n                        // Prefetch auth pages since users likely to sign up\n                        const link1 = document.createElement('link');\n                        link1.rel = 'prefetch';\n                        link1.href = '/pricing';\n                        document.head.appendChild(link1);\n                        const link2 = document.createElement('link');\n                        link2.rel = 'prefetch';\n                        link2.href = '/auth/signup';\n                        document.head.appendChild(link2);\n                        const link3 = document.createElement('link');\n                        link3.rel = 'prefetch';\n                        link3.href = '/features';\n                        document.head.appendChild(link3);\n                    }\n                }[\"LandingPage.useEffect.prefetchPages\"];\n                // Prefetch after initial load\n                const prefetchTimer = setTimeout(prefetchPages, 1000);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearTimeout(prefetchTimer)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], []);\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black text-sm\",\n                        children: \"Loading RouKey...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                gridSize: 60,\n                opacity: 0.06,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-screen relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        gridSize: 40,\n                        opacity: 0.04,\n                        color: \"#ff6b35\",\n                        variant: \"tech\",\n                        animated: true,\n                        glowEffect: true,\n                        className: \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                style: {\n                    top: '200vh'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-screen relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        gridSize: 35,\n                        opacity: 0.08,\n                        color: \"#000000\",\n                        variant: \"premium\",\n                        animated: true,\n                        className: \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"/3xRDC4vi9aEd7HfgSAcZoEuTKU=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx":
/*!***********************************************************!*\
  !*** ./src/components/landing/EnhancedGridBackground.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EnhancedGridBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction EnhancedGridBackground(param) {\n    let { className = '', gridSize = 40, opacity = 0.1, color = '#000000', animated = false, glowEffect = false, variant = 'subtle' } = param;\n    const getVariantStyles = ()=>{\n        switch(variant){\n            case 'tech':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(color).concat(Math.round(opacity * 255).toString(16).padStart(2, '0'), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(color).concat(Math.round(opacity * 255).toString(16).padStart(2, '0'), \" 1px, transparent 1px),\\n            radial-gradient(circle at 50% 50%, \").concat(color).concat(Math.round(opacity * 0.3 * 255).toString(16).padStart(2, '0'), \" 2px, transparent 2px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 4, \"px \").concat(gridSize * 4, \"px\"),\n                    animation: animated ? 'tech-grid-move 30s linear infinite' : 'none'\n                };\n            case 'premium':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(color).concat(Math.round(opacity * 255).toString(16).padStart(2, '0'), \" 0.5px, transparent 0.5px),\\n            linear-gradient(90deg, \").concat(color).concat(Math.round(opacity * 255).toString(16).padStart(2, '0'), \" 0.5px, transparent 0.5px),\\n            linear-gradient(\").concat(color).concat(Math.round(opacity * 0.5 * 255).toString(16).padStart(2, '0'), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(color).concat(Math.round(opacity * 0.5 * 255).toString(16).padStart(2, '0'), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px\"),\n                    animation: animated ? 'premium-grid-float 40s ease-in-out infinite' : 'none'\n                };\n            default:\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(color).concat(Math.round(opacity * 255).toString(16).padStart(2, '0'), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(color).concat(Math.round(opacity * 255).toString(16).padStart(2, '0'), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px\"),\n                    animation: animated ? 'subtle-grid-drift 25s linear infinite' : 'none'\n                };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...getVariantStyles(),\n                    zIndex: 1,\n                    filter: glowEffect ? 'drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))' : 'none'\n                },\n                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                    [\n                        \"cdf0235daf430a20\",\n                        [\n                            gridSize,\n                            gridSize,\n                            gridSize * 0.5,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.7,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1\n                        ]\n                    ]\n                ]) + \" \" + \"absolute inset-0 pointer-events-none \".concat(className)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\EnhancedGridBackground.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"cdf0235daf430a20\",\n                dynamic: [\n                    gridSize,\n                    gridSize,\n                    gridSize * 0.5,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.7,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1\n                ],\n                children: \"@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\".concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_c = EnhancedGridBackground;\nvar _c;\n$RefreshReg$(_c, \"EnhancedGridBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\n"));

/***/ })

});