"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: () => (/* binding */ OrchestrationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationCanvas = (param)=>{\n    let { executionId, onComplete, onError, onCanvasStateChange, forceMaximize = false } = param;\n    _s();\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            const synthesisCompleteEvent = events.find({\n                \"OrchestrationCanvas.useEffect.synthesisCompleteEvent\": (event)=>event.type === 'synthesis_complete'\n            }[\"OrchestrationCanvas.useEffect.synthesisCompleteEvent\"]);\n            if (synthesisCompleteEvent && !orchestrationComplete) {\n                var _synthesisCompleteEvent_data;\n                setOrchestrationComplete(true);\n                const result = ((_synthesisCompleteEvent_data = synthesisCompleteEvent.data) === null || _synthesisCompleteEvent_data === void 0 ? void 0 : _synthesisCompleteEvent_data.result) || 'Orchestration completed successfully';\n                setFinalResult(result);\n                // Notify parent component\n                if (onComplete) {\n                    onComplete(result);\n                }\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            if (error && onError) {\n                onError(error);\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        // Keep isCanvasOpen as true so component doesn't disappear completely\n        // We'll hide it via CSS transform instead\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        // isCanvasOpen should already be true, but ensure it\n        setIsCanvasOpen(true);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(true, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(isCanvasOpen, isMinimized);\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Handle external maximize trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            if (forceMaximize && isMinimized) {\n                console.log('🎭 [DEBUG] External maximize trigger received!');\n                handleMaximize();\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        forceMaximize,\n        isMinimized\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log('🎭 [DEBUG] OrchestrationCanvas is rendering!', {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen && !isMinimized ? 'translate-x-0' : 'translate-x-full'\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out border-l border-orange-500/20 \".concat(isCanvasOpen && !isMinimized ? 'translate-x-0' : 'translate-x-full'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 top-0 h-full w-[2px] bg-gradient-to-b from-transparent via-orange-500 to-transparent animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-bold text-white text-lg tracking-wide\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full \".concat(orchestrationComplete ? 'bg-green-400' : 'bg-orange-400', \" animate-pulse\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300 font-medium\",\n                                                    children: orchestrationComplete ? 'Mission Complete' : 'Team Active'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-orange-300 font-medium\",\n                                            children: \"LIVE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleMinimize,\n                                    className: \"group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30\",\n                                    \"aria-label\": \"Minimize canvas\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 transition-transform group-hover:scale-110\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-hidden relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                            executionId: executionId,\n                            events: events,\n                            isConnected: isConnected,\n                            error: error,\n                            isComplete: orchestrationComplete\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(OrchestrationCanvas, \"KsTXJg8wJUpwnTOsKVHiLc1u04Q=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = OrchestrationCanvas;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\n"));

/***/ })

});