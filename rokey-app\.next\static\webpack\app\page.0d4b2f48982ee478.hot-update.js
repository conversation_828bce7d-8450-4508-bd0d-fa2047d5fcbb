"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx":
/*!***********************************************************!*\
  !*** ./src/components/landing/EnhancedGridBackground.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EnhancedGridBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction EnhancedGridBackground(param) {\n    let { className = '', gridSize = 40, opacity = 0.1, color = '#000000', animated = false, glowEffect = false, variant = 'subtle' } = param;\n    const getVariantStyles = ()=>{\n        // Convert color to rgba format for proper opacity handling\n        const getRgbaColor = (baseColor, alpha)=>{\n            if (baseColor === '#000000') {\n                return \"rgba(0, 0, 0, \".concat(alpha, \")\");\n            } else if (baseColor === '#ffffff') {\n                return \"rgba(255, 255, 255, \".concat(alpha, \")\");\n            } else if (baseColor === '#ff6b35') {\n                return \"rgba(255, 107, 53, \".concat(alpha, \")\");\n            } else {\n                // Fallback for other colors\n                return \"\".concat(baseColor).concat(Math.round(alpha * 255).toString(16).padStart(2, '0'));\n            }\n        };\n        switch(variant){\n            case 'tech':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, opacity), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, opacity), \" 1px, transparent 1px),\\n            radial-gradient(circle at 50% 50%, \").concat(getRgbaColor(color, opacity * 0.3), \" 2px, transparent 2px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 4, \"px \").concat(gridSize * 4, \"px\"),\n                    animation: animated ? 'tech-grid-move 30s linear infinite' : 'none'\n                };\n            case 'premium':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, opacity), \" 0.5px, transparent 0.5px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, opacity), \" 0.5px, transparent 0.5px),\\n            linear-gradient(\").concat(getRgbaColor(color, opacity * 0.5), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, opacity * 0.5), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px, \").concat(gridSize * 5, \"px \").concat(gridSize * 5, \"px\"),\n                    animation: animated ? 'premium-grid-float 40s ease-in-out infinite' : 'none'\n                };\n            default:\n                return {\n                    backgroundImage: \"\\n            linear-gradient(\".concat(getRgbaColor(color, opacity), \" 1px, transparent 1px),\\n            linear-gradient(90deg, \").concat(getRgbaColor(color, opacity), \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px\"),\n                    animation: animated ? 'subtle-grid-drift 25s linear infinite' : 'none'\n                };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    ...getVariantStyles(),\n                    zIndex: 1,\n                    filter: glowEffect ? 'drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))' : 'none'\n                },\n                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                    [\n                        \"cdf0235daf430a20\",\n                        [\n                            gridSize,\n                            gridSize,\n                            gridSize * 0.5,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.7,\n                            gridSize * 0.3,\n                            gridSize,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1,\n                            gridSize * 0.2,\n                            gridSize * -0.1,\n                            gridSize * 0.1\n                        ]\n                    ]\n                ]) + \" \" + \"absolute inset-0 pointer-events-none \".concat(className)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\EnhancedGridBackground.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"cdf0235daf430a20\",\n                dynamic: [\n                    gridSize,\n                    gridSize,\n                    gridSize * 0.5,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.7,\n                    gridSize * 0.3,\n                    gridSize,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1,\n                    gridSize * 0.2,\n                    gridSize * -0.1,\n                    gridSize * 0.1\n                ],\n                children: \"@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\".concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize, \"px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-moz-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);-o-transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg);transform:translate(\").concat(gridSize * 0.5, \"px,\").concat(gridSize * 0.3, \"px)rotate(.5deg)}50%{-webkit-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-moz-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);-o-transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg);transform:translate(\").concat(gridSize, \"px,\").concat(gridSize * 0.7, \"px)rotate(0deg)}75%{-webkit-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-moz-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);-o-transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg);transform:translate(\").concat(gridSize * 0.3, \"px,\").concat(gridSize, \"px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-moz-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);-o-transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01);transform:translate(\").concat(gridSize * 0.2, \"px,\").concat(gridSize * -0.1, \"px)scale(1.01)}50%{-webkit-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-moz-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);-o-transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99);transform:translate(\").concat(gridSize * 0.1, \"px,\").concat(gridSize * 0.2, \"px)scale(.99)}75%{-webkit-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-moz-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);-o-transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005);transform:translate(\").concat(gridSize * -0.1, \"px,\").concat(gridSize * 0.1, \"px)scale(1.005)}}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_c = EnhancedGridBackground;\nvar _c;\n$RefreshReg$(_c, \"EnhancedGridBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\n"));

/***/ })

});