"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/RoutingVisualization.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst routingExamples = [\n    {\n        id: 1,\n        prompt: \"Solve this complex math problem: 2x + 5 = 15\",\n        role: \"logic_reasoning\",\n        roleName: \"Logic & Reasoning\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 2,\n        prompt: \"Write a blog post about AI trends\",\n        role: \"writing\",\n        roleName: \"Writing & Content Creation\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 3,\n        prompt: \"Build a React component with TypeScript\",\n        role: \"coding_frontend\",\n        roleName: \"Frontend Development\",\n        model: \"Claude 4 Opus\",\n        provider: \"Anthropic\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 4,\n        prompt: \"Summarize this research paper\",\n        role: \"research_synthesis\",\n        roleName: \"Research & Analysis\",\n        model: \"DeepSeek R1 0528\",\n        provider: \"DeepSeek\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    }\n];\nfunction RoutingVisualization() {\n    _s();\n    const [activeExample, setActiveExample] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RoutingVisualization.useEffect\": ()=>{\n            const interval = setInterval({\n                \"RoutingVisualization.useEffect.interval\": ()=>{\n                    setIsAnimating(true);\n                    setTimeout({\n                        \"RoutingVisualization.useEffect.interval\": ()=>{\n                            setActiveExample({\n                                \"RoutingVisualization.useEffect.interval\": (prev)=>(prev + 1) % routingExamples.length\n                            }[\"RoutingVisualization.useEffect.interval\"]);\n                            setIsAnimating(false);\n                        }\n                    }[\"RoutingVisualization.useEffect.interval\"], 500);\n                }\n            }[\"RoutingVisualization.useEffect.interval\"], 5000); // Slower transition for better viewing\n            return ({\n                \"RoutingVisualization.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutingVisualization.useEffect\"];\n        }\n    }[\"RoutingVisualization.useEffect\"], []);\n    const currentExample = routingExamples[activeExample];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"py-20 bg-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2edd7b34c21e4641\",\n                children: \"@-webkit-keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-moz-keyframes flowCurrent{0%{-moz-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-moz-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-o-keyframes flowCurrent{0%{-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}@keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);-moz-transform:translatex(200%);-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}.current-flow.jsx-2edd7b34c21e4641{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite}.current-flow-delayed.jsx-2edd7b34c21e4641{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite;-webkit-animation-delay:1.5s;-moz-animation-delay:1.5s;-o-animation-delay:1.5s;animation-delay:1.5s}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-gray-50 to-white\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '60px 60px'\n                        },\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 opacity-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-1/4 left-1/4 w-96 h-96 bg-[#ff6b35]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#f7931e]/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-4xl sm:text-5xl font-bold text-black mb-6\",\n                                children: \"Introducing the AI Gateway Pattern\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-700 max-w-3xl mx-auto\",\n                                children: \"Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-green-500/5 rounded-3xl blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-16 items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-black mb-2\",\n                                                        children: \"User Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-600\",\n                                                        children: \"Your request enters RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-white border-2 \".concat(currentExample.borderColor, \" rounded-xl p-6 relative \").concat(currentExample.bgColor, \" shadow-lg \").concat(currentExample.glowColor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-gray-300 opacity-30 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-600 mb-3\",\n                                                                children: \"Incoming Request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-black font-medium mb-4 text-lg leading-relaxed transition-all duration-500\",\n                                                                children: [\n                                                                    '\"',\n                                                                    currentExample.prompt,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex items-center text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Analyzing prompt...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.3\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-black mb-2\",\n                                                        children: \"Intelligent Role Classification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-600\",\n                                                        children: \"AI analyzes & routes to optimal model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-white border-2 border-gray-200 rounded-xl p-6 relative shadow-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-40 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-orange-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 241,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"RouKey Smart Classifier\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(currentExample.icon, {\n                                                                        className: \"h-6 w-6 \".concat(currentExample.color, \" mx-auto mb-2\")\n                                                                    }),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Classified as\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Role ID: \",\n                                                                            currentExample.role\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"✓ Context Analysis\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-600\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"✓ Role Matching\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 267,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-600\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: 0.6\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xl font-semibold text-black mb-2\",\n                                                        children: \"Optimal Model Selection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-gray-600\",\n                                                        children: \"Routed to the perfect model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-white border-2 border-[#ff6b35]/30 rounded-xl p-6 relative shadow-xl shadow-[#ff6b35]/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-50 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-[#ff6b35]/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-bold text-black mb-1\",\n                                                                        children: currentExample.model\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-[#ff6b35] font-medium\",\n                                                                        children: currentExample.provider\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center mb-4 shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Assigned Role\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"⚡ Speed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-black font-medium\",\n                                                                                children: \"Optimal\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"\\uD83C\\uDFAF Accuracy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 349,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-black font-medium\",\n                                                                                children: \"High\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 flex items-center justify-center text-[#ff6b35]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm font-medium\",\n                                                                        children: \"Route Established\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.8s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-[#ff6b35]/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-[#ff6b35]/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-[#ff6b35]/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-[#ff6b35] opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-black to-[#ff6b35] current-flow shadow-lg shadow-[#ff6b35]/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-black current-flow-delayed shadow-sm shadow-black/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex justify-center mt-12 space-x-2\",\n                                children: routingExamples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveExample(index),\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-3 h-3 rounded-full transition-all duration-200 \".concat(index === activeExample ? 'bg-[#ff6b35] scale-125' : 'bg-gray-500 hover:bg-gray-400')\n                                    }, example.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-black mb-2\",\n                                        children: \"Role-Based Classification\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-600\",\n                                        children: \"RouKey Smart Classifier analyzes context and classifies requests into 15+ specialized roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-black mb-2\",\n                                        children: \"Smart Model Matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-600\",\n                                        children: \"Each role routes to your pre-configured optimal model for maximum performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-black mb-2\",\n                                        children: \"Contextual Continuity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-600\",\n                                        children: \"Maintains conversation context and role consistency across multi-turn interactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-black mb-2\",\n                                        children: \"Fallback Protection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-600\",\n                                        children: \"Automatic fallback to default general chat model when no role match is found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.2\n                        },\n                        className: \"mt-24 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h4, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-4xl font-bold text-black mb-6\",\n                                        children: \"15+ Built-in Role Types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: 0.1\n                                        },\n                                        className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                        children: [\n                                            \"Each role is intelligently optimized for specific use cases, with the power to create\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-[#ff6b35] font-semibold\",\n                                                children: \" custom roles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" for your unique workflows\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-[#f7931e]/5 rounded-3xl blur-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 800 600\",\n                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-full h-full opacity-20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                    className: \"jsx-2edd7b34c21e4641\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"connectionGradient\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"0%\",\n                                                        className: \"jsx-2edd7b34c21e4641\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#ff6b35\",\n                                                                stopOpacity: \"0.3\",\n                                                                className: \"jsx-2edd7b34c21e4641\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#f7931e\",\n                                                                stopOpacity: \"0.6\",\n                                                                className: \"jsx-2edd7b34c21e4641\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#ff6b35\",\n                                                                stopOpacity: \"0.3\",\n                                                                className: \"jsx-2edd7b34c21e4641\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.path, {\n                                                    d: \"M100,150 Q400,50 700,150\",\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"1\",\n                                                    fill: \"none\",\n                                                    initial: {\n                                                        pathLength: 0\n                                                    },\n                                                    animate: {\n                                                        pathLength: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        repeatType: \"loop\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.path, {\n                                                    d: \"M100,300 Q400,200 700,300\",\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"1\",\n                                                    fill: \"none\",\n                                                    initial: {\n                                                        pathLength: 0\n                                                    },\n                                                    animate: {\n                                                        pathLength: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        repeatType: \"loop\",\n                                                        delay: 1\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.path, {\n                                                    d: \"M100,450 Q400,350 700,450\",\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"1\",\n                                                    fill: \"none\",\n                                                    initial: {\n                                                        pathLength: 0\n                                                    },\n                                                    animate: {\n                                                        pathLength: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        repeatType: \"loop\",\n                                                        delay: 2\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            [\n                                                {\n                                                    name: 'General Chat',\n                                                    icon: '💬',\n                                                    category: 'communication',\n                                                    description: 'Everyday conversations',\n                                                    delay: 0\n                                                },\n                                                {\n                                                    name: 'Logic & Reasoning',\n                                                    icon: '🧠',\n                                                    category: 'featured',\n                                                    description: 'Complex problem solving',\n                                                    delay: 0.1\n                                                },\n                                                {\n                                                    name: 'Writing & Content',\n                                                    icon: '✍️',\n                                                    category: 'creative',\n                                                    description: 'Content creation & editing',\n                                                    delay: 0.2\n                                                },\n                                                {\n                                                    name: 'Frontend Coding',\n                                                    icon: '⚛️',\n                                                    category: 'featured',\n                                                    description: 'UI/UX development',\n                                                    delay: 0.3\n                                                },\n                                                {\n                                                    name: 'Backend Coding',\n                                                    icon: '⚙️',\n                                                    category: 'technical',\n                                                    description: 'Server & API development',\n                                                    delay: 0.4\n                                                },\n                                                {\n                                                    name: 'Research & Analysis',\n                                                    icon: '🔬',\n                                                    category: 'featured',\n                                                    description: 'Data insights & research',\n                                                    delay: 0.5\n                                                },\n                                                {\n                                                    name: 'Summarization',\n                                                    icon: '📝',\n                                                    category: 'productivity',\n                                                    description: 'Content summarization',\n                                                    delay: 0.6\n                                                },\n                                                {\n                                                    name: 'Translation',\n                                                    icon: '🌐',\n                                                    category: 'featured',\n                                                    description: 'Multi-language support',\n                                                    delay: 0.7\n                                                },\n                                                {\n                                                    name: 'Data Extraction',\n                                                    icon: '📊',\n                                                    category: 'technical',\n                                                    description: 'Information extraction',\n                                                    delay: 0.8\n                                                },\n                                                {\n                                                    name: 'Brainstorming',\n                                                    icon: '💡',\n                                                    category: 'featured',\n                                                    description: 'Idea generation',\n                                                    delay: 0.9\n                                                },\n                                                {\n                                                    name: 'Education',\n                                                    icon: '🎓',\n                                                    category: 'learning',\n                                                    description: 'Teaching & explanations',\n                                                    delay: 1.0\n                                                },\n                                                {\n                                                    name: 'Audio Transcription',\n                                                    icon: '🎵',\n                                                    category: 'featured',\n                                                    description: 'Speech to text',\n                                                    delay: 1.1\n                                                }\n                                            ].map((role, index)=>{\n                                                const isFeatured = role.category === 'featured';\n                                                const cardStyles = isFeatured ? 'bg-gradient-to-br from-[#ff6b35] via-[#ff7043] to-[#f7931e] text-white border-[#ff6b35]/30 shadow-orange-500/25' : 'bg-white text-gray-900 border-gray-200 shadow-gray-900/10';\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0.8,\n                                                        y: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        scale: 1,\n                                                        y: 0\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    transition: {\n                                                        delay: role.delay,\n                                                        duration: 0.5\n                                                    },\n                                                    className: \"group relative\",\n                                                    children: [\n                                                        isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute -inset-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl blur-sm opacity-30 group-hover:opacity-60 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative \".concat(cardStyles, \" rounded-2xl p-6 border-2 hover:border-[#ff6b35]/60 transition-all duration-300 shadow-xl hover:shadow-2xl backdrop-blur-sm overflow-hidden\"),\n                                                            children: [\n                                                                isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 opacity-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundImage: \"\\n                                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),\\n                                radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),\\n                                radial-gradient(circle at 40% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                              \",\n                                                                            backgroundSize: '30px 30px, 25px 25px, 35px 35px',\n                                                                            animation: 'float 6s ease-in-out infinite'\n                                                                        },\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                !isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 opacity-5\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundImage: \"\\n                                linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),\\n                                linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px)\\n                              \",\n                                                                            backgroundSize: '20px 20px'\n                                                                        },\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 642,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-5xl group-hover:scale-125 transition-transform duration-300 filter \".concat(isFeatured ? 'drop-shadow-lg' : 'drop-shadow-md'),\n                                                                                    children: role.icon\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                    lineNumber: 659,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 pointer-events-none\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-2 right-2 w-1 h-1 bg-white/60 rounded-full animate-ping\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                            lineNumber: 665,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            style: {\n                                                                                                animationDelay: '1s'\n                                                                                            },\n                                                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute bottom-2 left-2 w-1 h-1 bg-white/40 rounded-full animate-ping\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                            lineNumber: 666,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                    lineNumber: 664,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"font-bold text-sm mb-2 leading-tight \".concat(isFeatured ? 'text-white' : 'text-gray-900'),\n                                                                            children: role.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs leading-relaxed \".concat(isFeatured ? 'text-white/80' : 'text-gray-600'),\n                                                                            children: role.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-2xl \".concat(isFeatured ? 'bg-gradient-to-r from-white/10 to-white/5' : 'bg-gradient-to-r from-[#ff6b35]/5 to-[#f7931e]/5', \" opacity-0 group-hover:opacity-100 transition-opacity duration-300\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-3 right-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-2 h-2 bg-white/80 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, role.name, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    scale: 1,\n                                                    y: 0\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                transition: {\n                                                    delay: 1.2,\n                                                    duration: 0.5\n                                                },\n                                                whileHover: {\n                                                    scale: 1.08,\n                                                    y: -8\n                                                },\n                                                className: \"group relative col-span-2 md:col-span-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute -inset-1 bg-gradient-to-r from-[#ff6b35] via-[#ff7043] to-[#f7931e] rounded-2xl blur-sm opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative bg-gradient-to-br from-[#ff6b35] via-[#ff7043] to-[#f7931e] rounded-2xl p-6 border-2 border-dashed border-white/40 hover:border-white/80 transition-all duration-300 shadow-2xl shadow-orange-500/30 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 opacity-15\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        backgroundImage: \"\\n                          radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.3) 2px, transparent 2px),\\n                          radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),\\n                          linear-gradient(45deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                          linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                        \",\n                                                                        backgroundSize: '30px 30px, 20px 20px, 15px 15px, 15px 15px',\n                                                                        animation: 'float 8s ease-in-out infinite, grid-move 12s linear infinite'\n                                                                    },\n                                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 pointer-events-none\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-4 right-6 w-1.5 h-1.5 bg-white/70 rounded-full animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            animationDelay: '1s'\n                                                                        },\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute bottom-6 left-4 w-1 h-1 bg-white/50 rounded-full animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 729,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            animationDelay: '2s'\n                                                                        },\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-1/2 left-1/2 w-0.5 h-0.5 bg-white/60 rounded-full animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative z-10 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"relative mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-5xl group-hover:scale-125 transition-transform duration-300 filter drop-shadow-2xl\",\n                                                                                children: \"\\uD83C\\uDFAF\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 736,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 text-5xl group-hover:scale-125 transition-transform duration-300 filter blur-sm opacity-50\",\n                                                                                children: \"\\uD83C\\uDFAF\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 740,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white font-bold text-sm mb-2 drop-shadow-lg\",\n                                                                        children: \"Custom Roles\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-white/90 text-xs leading-relaxed\",\n                                                                        children: \"Create your own specialized roles\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute inset-0 rounded-2xl border-2 border-white/60 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute top-2 left-2 w-3 h-3 border-l-2 border-t-2 border-white/60 rounded-tl-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"absolute bottom-2 right-2 w-3 h-3 border-r-2 border-b-2 border-white/60 rounded-br-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: 1.5\n                                        },\n                                        className: \"text-center mt-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2edd7b34c21e4641\" + \" \" + \"inline-flex items-center bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-full px-8 py-4 border border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"flex items-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl font-bold text-white\",\n                                                                children: \"15+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400\",\n                                                                children: \"Built-in Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-px h-8 bg-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl font-bold text-[#ff6b35]\",\n                                                                children: \"∞\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400\",\n                                                                children: \"Custom Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-px h-8 bg-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl font-bold text-white\",\n                                                                children: \"100%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-400\",\n                                                                children: \"Accuracy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"mt-20 bg-gradient-to-br from-gray-900/50 to-black/50 rounded-2xl p-8 border border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl font-bold text-white mb-4\",\n                                        children: \"Create Custom Roles for Your Workflow\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 max-w-3xl mx-auto\",\n                                        children: \"Beyond our 15+ built-in roles, RouKey empowers you to create custom roles tailored to your specific needs. Define role patterns, assign optimal models, and let our AI classifier automatically route requests.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Define Role Pattern\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Describe the types of requests this role should handle. Our AI learns from your examples and keywords.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Example Pattern:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-[#ff6b35] font-mono\",\n                                                        children: '\"SQL queries, database optimization, schema design\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-gray-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Assign Optimal Model\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Choose which model performs best for this role from your available providers and configurations.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Model Selection:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 837,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-white font-medium\",\n                                                        children: \"Claude 3.5 Sonnet → Database Expert\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#f7931e] to-[#ff6b35] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-400/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Automatic Routing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Our RouKey Smart Classifier automatically detects and routes matching requests to your custom role.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Auto-Detection:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-sm text-[#f7931e]\",\n                                                        children: \"✓ Pattern Recognition Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2edd7b34c21e4641\" + \" \" + \"text-center mt-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2edd7b34c21e4641\" + \" \" + \"inline-flex items-center bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-lg px-6 py-3 text-white font-medium shadow-lg shadow-orange-500/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-2edd7b34c21e4641\",\n                                        children: \"\\uD83D\\uDE80 Start Creating Custom Roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 794,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingVisualization, \"SlEVdab/r4hG20wt70ej7grkDC0=\");\n_c = RoutingVisualization;\nvar _c;\n$RefreshReg$(_c, \"RoutingVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\n"));

/***/ })

});