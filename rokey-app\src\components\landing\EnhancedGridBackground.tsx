'use client';

import { useEffect, useRef } from 'react';

interface EnhancedGridBackgroundProps {
  className?: string;
  gridSize?: number;
  opacity?: number;
  color?: string;
  animated?: boolean;
  glowEffect?: boolean;
  variant?: 'subtle' | 'tech' | 'premium';
}

export default function EnhancedGridBackground({ 
  className = '', 
  gridSize = 40,
  opacity = 0.1,
  color = '#000000',
  animated = false,
  glowEffect = false,
  variant = 'subtle'
}: EnhancedGridBackgroundProps) {
  
  const getVariantStyles = () => {
    switch (variant) {
      case 'tech':
        return {
          backgroundImage: `
            linear-gradient(${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')} 1px, transparent 1px),
            linear-gradient(90deg, ${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')} 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, ${color}${Math.round(opacity * 0.3 * 255).toString(16).padStart(2, '0')} 2px, transparent 2px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px, ${gridSize * 4}px ${gridSize * 4}px`,
          animation: animated ? 'tech-grid-move 30s linear infinite' : 'none',
        };
      
      case 'premium':
        return {
          backgroundImage: `
            linear-gradient(${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')} 0.5px, transparent 0.5px),
            linear-gradient(90deg, ${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')} 0.5px, transparent 0.5px),
            linear-gradient(${color}${Math.round(opacity * 0.5 * 255).toString(16).padStart(2, '0')} 1px, transparent 1px),
            linear-gradient(90deg, ${color}${Math.round(opacity * 0.5 * 255).toString(16).padStart(2, '0')} 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px, ${gridSize * 5}px ${gridSize * 5}px, ${gridSize * 5}px ${gridSize * 5}px`,
          animation: animated ? 'premium-grid-float 40s ease-in-out infinite' : 'none',
        };
      
      default: // subtle
        return {
          backgroundImage: `
            linear-gradient(${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')} 1px, transparent 1px),
            linear-gradient(90deg, ${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')} 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`,
          animation: animated ? 'subtle-grid-drift 25s linear infinite' : 'none',
        };
    }
  };

  return (
    <>
      <div
        className={`absolute inset-0 pointer-events-none ${className}`}
        style={{
          ...getVariantStyles(),
          zIndex: 1,
          filter: glowEffect ? 'drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))' : 'none',
        }}
      />
      
      {/* CSS Animations */}
      <style jsx>{`
        @keyframes subtle-grid-drift {
          0% { transform: translate(0, 0); }
          100% { transform: translate(${gridSize}px, ${gridSize}px); }
        }
        
        @keyframes tech-grid-move {
          0% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(${gridSize * 0.5}px, ${gridSize * 0.3}px) rotate(0.5deg); }
          50% { transform: translate(${gridSize}px, ${gridSize * 0.7}px) rotate(0deg); }
          75% { transform: translate(${gridSize * 0.3}px, ${gridSize}px) rotate(-0.5deg); }
          100% { transform: translate(0, 0) rotate(0deg); }
        }
        
        @keyframes premium-grid-float {
          0%, 100% { transform: translate(0, 0) scale(1); }
          25% { transform: translate(${gridSize * 0.2}px, ${gridSize * -0.1}px) scale(1.01); }
          50% { transform: translate(${gridSize * 0.1}px, ${gridSize * 0.2}px) scale(0.99); }
          75% { transform: translate(${gridSize * -0.1}px, ${gridSize * 0.1}px) scale(1.005); }
        }
      `}</style>
    </>
  );
}
