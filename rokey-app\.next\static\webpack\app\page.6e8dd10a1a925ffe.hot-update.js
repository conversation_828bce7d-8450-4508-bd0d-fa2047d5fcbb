"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/serviceWorker */ \"(app-pages-browser)/./src/utils/serviceWorker.ts\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/HeroSection */ \"(app-pages-browser)/./src/components/landing/HeroSection.tsx\");\n/* harmony import */ var _components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/TrustBadges */ \"(app-pages-browser)/./src/components/landing/TrustBadges.tsx\");\n/* harmony import */ var _components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/landing/FeaturesSection */ \"(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\");\n/* harmony import */ var _components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/landing/RoutingVisualization */ \"(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\");\n/* harmony import */ var _components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/landing/TestimonialsSection */ \"(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\");\n/* harmony import */ var _components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/CTASection */ \"(app-pages-browser)/./src/components/landing/CTASection.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_CSSGridBackground__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/landing/CSSGridBackground */ \"(app-pages-browser)/./src/components/landing/CSSGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            // Register service worker for caching\n            (0,_utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__.registerServiceWorker)({\n                onSuccess: {\n                    \"LandingPage.useEffect\": ()=>console.log('✅ Service Worker registered for caching')\n                }[\"LandingPage.useEffect\"],\n                onUpdate: {\n                    \"LandingPage.useEffect\": ()=>console.log('🔄 New content available')\n                }[\"LandingPage.useEffect\"],\n                onError: {\n                    \"LandingPage.useEffect\": (error)=>console.warn('⚠️ Service Worker registration failed:', error)\n                }[\"LandingPage.useEffect\"]\n            });\n            setIsLoaded(true);\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Prefetch likely next pages on hover\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (true) {\n                const prefetchPages = {\n                    \"LandingPage.useEffect.prefetchPages\": ()=>{\n                        // Prefetch auth pages since users likely to sign up\n                        const link1 = document.createElement('link');\n                        link1.rel = 'prefetch';\n                        link1.href = '/pricing';\n                        document.head.appendChild(link1);\n                        const link2 = document.createElement('link');\n                        link2.rel = 'prefetch';\n                        link2.href = '/auth/signup';\n                        document.head.appendChild(link2);\n                        const link3 = document.createElement('link');\n                        link3.rel = 'prefetch';\n                        link3.href = '/features';\n                        document.head.appendChild(link3);\n                    }\n                }[\"LandingPage.useEffect.prefetchPages\"];\n                // Prefetch after initial load\n                const prefetchTimer = setTimeout(prefetchPages, 1000);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearTimeout(prefetchTimer)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], []);\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black text-sm\",\n                        children: \"Loading RouKey...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CSSGridBackground__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                gridSize: 50,\n                opacity: 0.08,\n                color: \"#000000\",\n                variant: \"lines\",\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"/3xRDC4vi9aEd7HfgSAcZoEuTKU=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/landing/CSSGridBackground.tsx":
/*!******************************************************!*\
  !*** ./src/components/landing/CSSGridBackground.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CSSGridBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction CSSGridBackground(param) {\n    let { className = '', gridSize = 40, opacity = 0.1, color = '#000000', variant = 'lines' } = param;\n    const getBackgroundStyle = ()=>{\n        const colorWithOpacity = \"\".concat(color).concat(Math.round(opacity * 255).toString(16).padStart(2, '0'));\n        switch(variant){\n            case 'dots':\n                return {\n                    backgroundImage: \"radial-gradient(circle, \".concat(colorWithOpacity, \" 1px, transparent 1px)\"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px\")\n                };\n            case 'lines':\n                return {\n                    backgroundImage: \"\\n            linear-gradient(to right, \".concat(colorWithOpacity, \" 1px, transparent 1px),\\n            linear-gradient(to bottom, \").concat(colorWithOpacity, \" 1px, transparent 1px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px\")\n                };\n            case 'both':\n                return {\n                    backgroundImage: \"\\n            radial-gradient(circle, \".concat(colorWithOpacity, \" 1px, transparent 1px),\\n            linear-gradient(to right, \").concat(colorWithOpacity, \" 0.5px, transparent 0.5px),\\n            linear-gradient(to bottom, \").concat(colorWithOpacity, \" 0.5px, transparent 0.5px)\\n          \"),\n                    backgroundSize: \"\".concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px, \").concat(gridSize, \"px \").concat(gridSize, \"px\")\n                };\n            default:\n                return {};\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute inset-0 pointer-events-none \".concat(className),\n        style: {\n            ...getBackgroundStyle(),\n            zIndex: 1\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\CSSGridBackground.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = CSSGridBackground;\nvar _c;\n$RefreshReg$(_c, \"CSSGridBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/CSSGridBackground.tsx\n"));

/***/ })

});