"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx":
/*!********************************************************!*\
  !*** ./src/components/landing/TestimonialsSection.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"Lead Developer\",\n        company: \"TechFlow Inc\",\n        avatar: \"SC\",\n        content: \"RouKey has revolutionized how we handle AI requests. The intelligent routing saves us hours of manual configuration, and the cost optimization features have reduced our AI spending by 40%.\",\n        rating: 5\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"CTO\",\n        company: \"DataVision Labs\",\n        avatar: \"MR\",\n        content: \"The automatic failover feature is a game-changer. We never worry about API limits or downtime anymore. RouKey just handles everything seamlessly in the background.\",\n        rating: 5\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"AI Engineer\",\n        company: \"InnovateCorp\",\n        avatar: \"EW\",\n        content: \"Having access to 300+ models through one API is incredible. The performance analytics help us optimize our model selection, and the security features give us peace of mind.\",\n        rating: 5\n    },\n    {\n        name: \"David Kim\",\n        role: \"Product Manager\",\n        company: \"StartupXYZ\",\n        avatar: \"DK\",\n        content: \"RouKey's intelligent routing is like having an AI expert on our team. It automatically picks the best model for each task, improving our output quality significantly.\",\n        rating: 5\n    },\n    {\n        name: \"Lisa Thompson\",\n        role: \"Engineering Manager\",\n        company: \"ScaleUp Solutions\",\n        avatar: \"LT\",\n        content: \"The comprehensive analytics and cost tracking features are outstanding. We can now make data-driven decisions about our AI infrastructure and optimize our spending.\",\n        rating: 5\n    },\n    {\n        name: \"Alex Johnson\",\n        role: \"Senior Developer\",\n        company: \"CloudTech Pro\",\n        avatar: \"AJ\",\n        content: \"RouKey's enterprise security and reliability features make it perfect for production environments. The team management features are exactly what we needed.\",\n        rating: 5\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Trusted by Developers Worldwide\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"See what developers and teams are saying about RouKey's intelligent AI routing platform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                delay: index * 0.1\n                            },\n                            className: \"bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        ...Array(testimonial.rating)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-5 w-5 text-[#ff6b35]\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6 leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-full flex items-center justify-center text-white font-semibold mr-4\",\n                                            children: testimonial.avatar\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: testimonial.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        testimonial.role,\n                                                        \" at \",\n                                                        testimonial.company\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, testimonial.name, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\TestimonialsSection.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\n"));

/***/ })

});