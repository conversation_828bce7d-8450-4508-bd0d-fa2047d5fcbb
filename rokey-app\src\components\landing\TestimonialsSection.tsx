'use client';

import { motion } from 'framer-motion';
import { StarIcon } from '@heroicons/react/24/solid';

const testimonials = [
  {
    name: "<PERSON>",
    role: "Lead Developer",
    company: "TechFlow Inc",
    avatar: "SC",
    content: "<PERSON><PERSON><PERSON><PERSON> has revolutionized how we handle AI requests. The intelligent routing saves us hours of manual configuration, and the cost optimization features have reduced our AI spending by 40%.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "CTO",
    company: "DataVision Labs",
    avatar: "MR",
    content: "The automatic failover feature is a game-changer. We never worry about API limits or downtime anymore. <PERSON><PERSON><PERSON><PERSON> just handles everything seamlessly in the background.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "AI Engineer",
    company: "InnovateCorp",
    avatar: "EW",
    content: "Having access to 300+ models through one API is incredible. The performance analytics help us optimize our model selection, and the security features give us peace of mind.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Product Manager",
    company: "StartupXYZ",
    avatar: "DK",
    content: "<PERSON><PERSON><PERSON><PERSON>'s intelligent routing is like having an AI expert on our team. It automatically picks the best model for each task, improving our output quality significantly.",
    rating: 5
  },
  {
    name: "<PERSON>",
    role: "Engineering Manager",
    company: "ScaleUp Solutions",
    avatar: "LT",
    content: "The comprehensive analytics and cost tracking features are outstanding. We can now make data-driven decisions about our AI infrastructure and optimize our spending.",
    rating: 5
  },
  {
    name: "Alex Johnson",
    role: "Senior Developer",
    company: "CloudTech Pro",
    avatar: "AJ",
    content: "RouKey's enterprise security and reliability features make it perfect for production environments. The team management features are exactly what we needed.",
    rating: 5
  }
];

export default function TestimonialsSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4"
          >
            Trusted by Developers Worldwide
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            See what developers and teams are saying about RouKey's intelligent AI routing platform
          </motion.p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <StarIcon key={i} className="h-5 w-5 text-[#ff6b35]" />
                ))}
              </div>

              {/* Content */}
              <p className="text-gray-700 mb-6 leading-relaxed">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-full flex items-center justify-center text-white font-semibold mr-4">
                  {testimonial.avatar}
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">
                    {testimonial.role} at {testimonial.company}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>


      </div>
    </section>
  );
}
