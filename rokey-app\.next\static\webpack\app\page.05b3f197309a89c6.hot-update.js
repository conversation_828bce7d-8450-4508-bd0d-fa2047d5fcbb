"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/serviceWorker */ \"(app-pages-browser)/./src/utils/serviceWorker.ts\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/HeroSection */ \"(app-pages-browser)/./src/components/landing/HeroSection.tsx\");\n/* harmony import */ var _components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/TrustBadges */ \"(app-pages-browser)/./src/components/landing/TrustBadges.tsx\");\n/* harmony import */ var _components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/landing/FeaturesSection */ \"(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\");\n/* harmony import */ var _components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/landing/RoutingVisualization */ \"(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\");\n/* harmony import */ var _components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/landing/TestimonialsSection */ \"(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\");\n/* harmony import */ var _components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/CTASection */ \"(app-pages-browser)/./src/components/landing/CTASection.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_CSSGridBackground__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/landing/CSSGridBackground */ \"(app-pages-browser)/./src/components/landing/CSSGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            // Register service worker for caching\n            (0,_utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__.registerServiceWorker)({\n                onSuccess: {\n                    \"LandingPage.useEffect\": ()=>console.log('✅ Service Worker registered for caching')\n                }[\"LandingPage.useEffect\"],\n                onUpdate: {\n                    \"LandingPage.useEffect\": ()=>console.log('🔄 New content available')\n                }[\"LandingPage.useEffect\"],\n                onError: {\n                    \"LandingPage.useEffect\": (error)=>console.warn('⚠️ Service Worker registration failed:', error)\n                }[\"LandingPage.useEffect\"]\n            });\n            setIsLoaded(true);\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Prefetch likely next pages on hover\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (true) {\n                const prefetchPages = {\n                    \"LandingPage.useEffect.prefetchPages\": ()=>{\n                        // Prefetch auth pages since users likely to sign up\n                        const link1 = document.createElement('link');\n                        link1.rel = 'prefetch';\n                        link1.href = '/pricing';\n                        document.head.appendChild(link1);\n                        const link2 = document.createElement('link');\n                        link2.rel = 'prefetch';\n                        link2.href = '/auth/signup';\n                        document.head.appendChild(link2);\n                        const link3 = document.createElement('link');\n                        link3.rel = 'prefetch';\n                        link3.href = '/features';\n                        document.head.appendChild(link3);\n                    }\n                }[\"LandingPage.useEffect.prefetchPages\"];\n                // Prefetch after initial load\n                const prefetchTimer = setTimeout(prefetchPages, 1000);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearTimeout(prefetchTimer)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], []);\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black text-sm\",\n                        children: \"Loading RouKey...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CSSGridBackground__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                gridSize: 50,\n                opacity: 0.08,\n                color: \"#000000\",\n                variant: \"lines\",\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TrustBadges__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CTASection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"/3xRDC4vi9aEd7HfgSAcZoEuTKU=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNrQjtBQUNDO0FBQ0o7QUFDQTtBQUNRO0FBQ1U7QUFDRjtBQUNsQjtBQUNSO0FBQ3NCO0FBR3hELFNBQVNZOztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2QsK0NBQVFBLENBQUM7SUFFekNDLGdEQUFTQTtpQ0FBQztZQUNSLHNDQUFzQztZQUN0Q0MsMkVBQXFCQSxDQUFDO2dCQUNwQmEsU0FBUzs2Q0FBRSxJQUFNQyxRQUFRQyxHQUFHLENBQUM7O2dCQUM3QkMsUUFBUTs2Q0FBRSxJQUFNRixRQUFRQyxHQUFHLENBQUM7O2dCQUM1QkUsT0FBTzs2Q0FBRSxDQUFDQyxRQUFVSixRQUFRSyxJQUFJLENBQUMsMENBQTBDRDs7WUFDN0U7WUFFQU4sWUFBWTtRQUNkO2dDQUFHLEVBQUU7SUFFTCxzQ0FBc0M7SUFDdENiLGdEQUFTQTtpQ0FBQztZQUNSLElBQUksSUFBNkIsRUFBRTtnQkFDakMsTUFBTXFCOzJEQUFnQjt3QkFDcEIsb0RBQW9EO3dCQUNwRCxNQUFNQyxRQUFRQyxTQUFTQyxhQUFhLENBQUM7d0JBQ3JDRixNQUFNRyxHQUFHLEdBQUc7d0JBQ1pILE1BQU1JLElBQUksR0FBRzt3QkFDYkgsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNOO3dCQUUxQixNQUFNTyxRQUFRTixTQUFTQyxhQUFhLENBQUM7d0JBQ3JDSyxNQUFNSixHQUFHLEdBQUc7d0JBQ1pJLE1BQU1ILElBQUksR0FBRzt3QkFDYkgsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNDO3dCQUUxQixNQUFNQyxRQUFRUCxTQUFTQyxhQUFhLENBQUM7d0JBQ3JDTSxNQUFNTCxHQUFHLEdBQUc7d0JBQ1pLLE1BQU1KLElBQUksR0FBRzt3QkFDYkgsU0FBU0ksSUFBSSxDQUFDQyxXQUFXLENBQUNFO29CQUM1Qjs7Z0JBRUEsOEJBQThCO2dCQUM5QixNQUFNQyxnQkFBZ0JDLFdBQVdYLGVBQWU7Z0JBQ2hEOzZDQUFPLElBQU1ZLGFBQWFGOztZQUM1QjtRQUNGO2dDQUFHLEVBQUU7SUFFTCxJQUFJLENBQUNuQixVQUFVO1FBQ2IscUJBQ0UsOERBQUNzQjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJMUM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUN6Qiw4RUFBaUJBO2dCQUNoQjJCLFVBQVU7Z0JBQ1ZDLFNBQVM7Z0JBQ1RDLE9BQU07Z0JBQ05DLFNBQVE7Z0JBQ1JMLFdBQVU7Ozs7OzswQkFJWiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDakMseUVBQWFBOzs7OztrQ0FFZCw4REFBQ3VDOzswQ0FDQyw4REFBQ3RDLHVFQUFXQTs7Ozs7MENBQ1osOERBQUNDLHVFQUFXQTs7Ozs7MENBQ1osOERBQUNFLGdGQUFvQkE7Ozs7OzBDQUNyQiw4REFBQ0QsMkVBQWVBOzs7OzswQ0FDaEIsOERBQUNFLCtFQUFtQkE7Ozs7OzBDQUNwQiw4REFBQ0Msc0VBQVVBOzs7Ozs7Ozs7OztrQ0FHYiw4REFBQ0MsbUVBQU1BOzs7Ozs7Ozs7Ozs7Ozs7OztBQUlmO0dBaEZ3QkU7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHJlZ2lzdGVyU2VydmljZVdvcmtlciB9IGZyb20gJ0AvdXRpbHMvc2VydmljZVdvcmtlcic7XG5pbXBvcnQgTGFuZGluZ05hdmJhciBmcm9tICdAL2NvbXBvbmVudHMvbGFuZGluZy9MYW5kaW5nTmF2YmFyJztcbmltcG9ydCBIZXJvU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvbGFuZGluZy9IZXJvU2VjdGlvbic7XG5pbXBvcnQgVHJ1c3RCYWRnZXMgZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvVHJ1c3RCYWRnZXMnO1xuaW1wb3J0IEZlYXR1cmVzU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvbGFuZGluZy9GZWF0dXJlc1NlY3Rpb24nO1xuaW1wb3J0IFJvdXRpbmdWaXN1YWxpemF0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9sYW5kaW5nL1JvdXRpbmdWaXN1YWxpemF0aW9uJztcbmltcG9ydCBUZXN0aW1vbmlhbHNTZWN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9sYW5kaW5nL1Rlc3RpbW9uaWFsc1NlY3Rpb24nO1xuaW1wb3J0IENUQVNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvQ1RBU2VjdGlvbic7XG5pbXBvcnQgRm9vdGVyIGZyb20gJ0AvY29tcG9uZW50cy9sYW5kaW5nL0Zvb3Rlcic7XG5pbXBvcnQgQ1NTR3JpZEJhY2tncm91bmQgZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvQ1NTR3JpZEJhY2tncm91bmQnO1xuaW1wb3J0IEVuaGFuY2VkR3JpZEJhY2tncm91bmQgZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvRW5oYW5jZWRHcmlkQmFja2dyb3VuZCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExhbmRpbmdQYWdlKCkge1xuICBjb25zdCBbaXNMb2FkZWQsIHNldElzTG9hZGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFJlZ2lzdGVyIHNlcnZpY2Ugd29ya2VyIGZvciBjYWNoaW5nXG4gICAgcmVnaXN0ZXJTZXJ2aWNlV29ya2VyKHtcbiAgICAgIG9uU3VjY2VzczogKCkgPT4gY29uc29sZS5sb2coJ+KchSBTZXJ2aWNlIFdvcmtlciByZWdpc3RlcmVkIGZvciBjYWNoaW5nJyksXG4gICAgICBvblVwZGF0ZTogKCkgPT4gY29uc29sZS5sb2coJ/CflIQgTmV3IGNvbnRlbnQgYXZhaWxhYmxlJyksXG4gICAgICBvbkVycm9yOiAoZXJyb3IpID0+IGNvbnNvbGUud2Fybign4pqg77iPIFNlcnZpY2UgV29ya2VyIHJlZ2lzdHJhdGlvbiBmYWlsZWQ6JywgZXJyb3IpXG4gICAgfSk7XG5cbiAgICBzZXRJc0xvYWRlZCh0cnVlKTtcbiAgfSwgW10pO1xuXG4gIC8vIFByZWZldGNoIGxpa2VseSBuZXh0IHBhZ2VzIG9uIGhvdmVyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCBwcmVmZXRjaFBhZ2VzID0gKCkgPT4ge1xuICAgICAgICAvLyBQcmVmZXRjaCBhdXRoIHBhZ2VzIHNpbmNlIHVzZXJzIGxpa2VseSB0byBzaWduIHVwXG4gICAgICAgIGNvbnN0IGxpbmsxID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbGluaycpO1xuICAgICAgICBsaW5rMS5yZWwgPSAncHJlZmV0Y2gnO1xuICAgICAgICBsaW5rMS5ocmVmID0gJy9wcmljaW5nJztcbiAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rMSk7XG5cbiAgICAgICAgY29uc3QgbGluazIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdsaW5rJyk7XG4gICAgICAgIGxpbmsyLnJlbCA9ICdwcmVmZXRjaCc7XG4gICAgICAgIGxpbmsyLmhyZWYgPSAnL2F1dGgvc2lnbnVwJztcbiAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rMik7XG5cbiAgICAgICAgY29uc3QgbGluazMgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdsaW5rJyk7XG4gICAgICAgIGxpbmszLnJlbCA9ICdwcmVmZXRjaCc7XG4gICAgICAgIGxpbmszLmhyZWYgPSAnL2ZlYXR1cmVzJztcbiAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rMyk7XG4gICAgICB9O1xuXG4gICAgICAvLyBQcmVmZXRjaCBhZnRlciBpbml0aWFsIGxvYWRcbiAgICAgIGNvbnN0IHByZWZldGNoVGltZXIgPSBzZXRUaW1lb3V0KHByZWZldGNoUGFnZXMsIDEwMDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dChwcmVmZXRjaFRpbWVyKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICBpZiAoIWlzTG9hZGVkKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1bI2ZmNmIzNV0gbXgtYXV0byBtYi0yXCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibGFjayB0ZXh0LXNtXCI+TG9hZGluZyBSb3VLZXkuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctd2hpdGUgcmVsYXRpdmVcIj5cbiAgICAgIHsvKiBHcmlkIEJhY2tncm91bmQgKi99XG4gICAgICA8Q1NTR3JpZEJhY2tncm91bmRcbiAgICAgICAgZ3JpZFNpemU9ezUwfVxuICAgICAgICBvcGFjaXR5PXswLjA4fVxuICAgICAgICBjb2xvcj1cIiMwMDAwMDBcIlxuICAgICAgICB2YXJpYW50PVwibGluZXNcIlxuICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wXCJcbiAgICAgIC8+XG5cbiAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgIDxMYW5kaW5nTmF2YmFyIC8+XG5cbiAgICAgICAgPG1haW4+XG4gICAgICAgICAgPEhlcm9TZWN0aW9uIC8+XG4gICAgICAgICAgPFRydXN0QmFkZ2VzIC8+XG4gICAgICAgICAgPFJvdXRpbmdWaXN1YWxpemF0aW9uIC8+XG4gICAgICAgICAgPEZlYXR1cmVzU2VjdGlvbiAvPlxuICAgICAgICAgIDxUZXN0aW1vbmlhbHNTZWN0aW9uIC8+XG4gICAgICAgICAgPENUQVNlY3Rpb24gLz5cbiAgICAgICAgPC9tYWluPlxuXG4gICAgICAgIDxGb290ZXIgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwicmVnaXN0ZXJTZXJ2aWNlV29ya2VyIiwiTGFuZGluZ05hdmJhciIsIkhlcm9TZWN0aW9uIiwiVHJ1c3RCYWRnZXMiLCJGZWF0dXJlc1NlY3Rpb24iLCJSb3V0aW5nVmlzdWFsaXphdGlvbiIsIlRlc3RpbW9uaWFsc1NlY3Rpb24iLCJDVEFTZWN0aW9uIiwiRm9vdGVyIiwiQ1NTR3JpZEJhY2tncm91bmQiLCJMYW5kaW5nUGFnZSIsImlzTG9hZGVkIiwic2V0SXNMb2FkZWQiLCJvblN1Y2Nlc3MiLCJjb25zb2xlIiwibG9nIiwib25VcGRhdGUiLCJvbkVycm9yIiwiZXJyb3IiLCJ3YXJuIiwicHJlZmV0Y2hQYWdlcyIsImxpbmsxIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwicmVsIiwiaHJlZiIsImhlYWQiLCJhcHBlbmRDaGlsZCIsImxpbmsyIiwibGluazMiLCJwcmVmZXRjaFRpbWVyIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJncmlkU2l6ZSIsIm9wYWNpdHkiLCJjb2xvciIsInZhcmlhbnQiLCJtYWluIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});